#!/usr/bin/env python3
"""
ICBM预警系统 v1.0 主程序
基于STK和ADK框架的洲际弹道导弹预警系统
"""

import asyncio
import sys
import os
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 使用统一的导入管理器
from src.utils.import_manager import get_import_manager
from src.utils.config_manager import get_config_manager
from src.utils.time_manager import get_time_manager

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class ICBMWarningSystem:
    """ICBM预警系统主类"""
    
    def __init__(self):
        """初始化ICBM预警系统V2.0 - 集成优化版本"""
        # 统一管理器
        self.import_manager = get_import_manager()
        self.config_manager = get_config_manager("config/constellation_config.yaml")
        self.time_manager = get_time_manager(self.config_manager)

        # 系统组件
        self.stk_manager = None
        self.missile_manager = None
        self.visibility_calculator = None
        self.gantt_generator = None
        self.output_manager = None

        # 系统状态
        self.agents = {}
        self.adk_agents = {}
        self.satellites = {}
        self.missiles = {}

        # 新增组件 - 基于项目成功经验
        self.satellite_agent_mapper = None
        self.meta_task_coordinator = None

        # 系统配置 - 现有项目检测已移至STK管理器

        # 导入所有必需组件
        self._import_all_components()

        logger.info("🚀 ICBM预警系统V2.0初始化完成")
        logger.info(f"   配置管理器: {type(self.config_manager).__name__}")
        logger.info(f"   时间管理器: {type(self.time_manager).__name__}")
        logger.info(f"   导入管理器: {type(self.import_manager).__name__}")

    def _import_all_components(self):
        """导入所有必需组件"""
        try:
            logger.info("📦 开始导入所有系统组件...")

            # 1. 导入ADK框架
            adk_success, self.adk_components = self.import_manager.import_adk_framework()
            if not adk_success:
                logger.error("❌ ADK框架导入失败，系统无法运行")
                raise RuntimeError("ADK框架是必需的")

            # 2. 导入STK接口
            stk_success, self.stk_components = self.import_manager.import_stk_interface()
            if not stk_success:
                logger.error("❌ STK接口导入失败，系统无法运行")
                raise RuntimeError("STK接口是必需的")

            # 3. 导入ADK智能体
            agents_success, self.agent_components = self.import_manager.import_adk_agents()
            if not agents_success:
                logger.error("❌ ADK智能体导入失败，系统无法运行")
                raise RuntimeError("ADK智能体是必需的")

            # 4. 导入可视化组件
            viz_success, self.viz_components = self.import_manager.import_visualization_components()
            if not viz_success:
                logger.warning("⚠️ 可视化组件导入失败，部分功能可能不可用")
                self.viz_components = {}

            # 5. 导入工具组件
            util_success, self.util_components = self.import_manager.import_utility_components()
            if not util_success:
                logger.warning("⚠️ 工具组件导入失败，部分功能可能不可用")
                self.util_components = {}

            # 6. 显示导入摘要
            import_summary = self.import_manager.get_import_summary()
            logger.info(f"📊 组件导入摘要:")
            logger.info(f"   成功: {import_summary['summary']['total_successful']}")
            logger.info(f"   失败: {import_summary['summary']['total_failed']}")
            logger.info(f"   成功率: {import_summary['summary']['success_rate']:.1%}")

        except Exception as e:
            logger.error(f"❌ 组件导入失败: {e}")
            raise RuntimeError(f"系统组件导入失败: {e}")

    def load_configuration(self) -> bool:
        """加载系统配置 - 使用统一配置管理器"""
        try:
            logger.info("📋 加载系统配置...")

            # 使用统一配置管理器重新加载配置
            if not self.config_manager.reload_config():
                logger.error("❌ 配置重新加载失败")
                return False

            # 获取配置摘要
            config_summary = self.config_manager.get_config_summary()
            logger.info(f"✅ 配置加载成功")
            logger.info(f"   配置文件: {config_summary['config_file']}")

            # 显示关键配置信息
            constellation = config_summary['constellation']
            logger.info(f"   星座名称: {constellation['name']}")
            logger.info(f"   星座类型: {constellation['type']}")
            logger.info(f"   轨道面数: {constellation['planes']}")
            logger.info(f"   每面卫星数: {constellation['satellites_per_plane']}")
            logger.info(f"   总卫星数: {constellation['total_satellites']}")

            # 显示时间配置信息
            simulation = config_summary['simulation']
            logger.info(f"   仿真开始时间: {simulation['start_time']}")
            logger.info(f"   仿真结束时间: {simulation['end_time']}")
            logger.info(f"   时间步长: {simulation['time_step']}秒")

            # 显示导弹配置信息
            missiles = config_summary['missiles']
            logger.info(f"   导弹威胁数量: {missiles['total_threats']}")

            return True

        except Exception as e:
            logger.error(f"❌ 配置加载失败: {e}")
            return False
    
    async def initialize_system(self) -> bool:
        """初始化系统组件 - 使用统一管理器"""
        try:
            logger.info("🔧 初始化系统组件...")

            # 1. 创建输出管理器 - 使用统一配置
            logger.info("📁 创建输出管理器...")
            if not hasattr(self, 'output_manager') or self.output_manager is None:
                OutputManager = self.util_components.get('OutputManager')
                if OutputManager:
                    self.output_manager = OutputManager()
                    logger.info(f"✅ 输出管理器创建成功，输出目录: {self.output_manager.session_dir}")
                else:
                    logger.error("❌ 输出管理器组件不可用")
                    return False
            else:
                logger.info(f"⏭️  使用现有输出管理器，输出目录: {self.output_manager.session_dir}")

            # 2. 初始化STK管理器 - 使用统一配置和时间管理
            logger.info("📡 初始化STK管理器...")

            # 获取STK管理器类
            STKManager = self.stk_components.get('STKManager')
            if not STKManager:
                logger.error("❌ STK管理器组件不可用")
                return False

            # 传递配置管理器给STK管理器
            self.stk_manager = STKManager(self.config_manager.get_config())

            # 连接STK - 使用统一配置
            stk_config = self.config_manager.get_stk_config()
            interactive_mode = stk_config.get('interactive', True)

            logger.info(f"🔌 启动STK软件 (交互模式: {interactive_mode})...")
            if not self.stk_manager.connect():
                logger.error("❌ STK连接失败")
                return False

            # 验证连接状态
            if not self.stk_manager.is_connected:
                logger.error("❌ STK连接状态验证失败")
                return False

            logger.info("✅ STK连接成功")
            
            # 3. 验证场景创建和时间设置
            if not self.stk_manager.scenario:
                logger.error("❌ 场景未创建")
                return False

            logger.info(f"✅ 场景验证成功: {self.stk_manager.scenario.InstanceName}")

            # 场景时间已在创建时设置，这里只做验证
            try:
                start_time = self.stk_manager.scenario_begin_time
                end_time = self.stk_manager.scenario_end_time
                if start_time and end_time:
                    logger.info(f"✅ 场景时间验证: {start_time} - {end_time}")
                else:
                    logger.error("❌ 场景时间未设置")
                    raise Exception("场景时间未设置")
            except Exception as e:
                logger.warning(f"⚠️  场景时间验证失败: {e}")
            
            # 4. 初始化核心组件
            logger.info("🛠️  初始化核心组件...")

            # 获取组件类
            MissileManager = self.stk_components.get('MissileManager')
            VisibilityCalculator = self.stk_components.get('VisibilityCalculator')
            GanttGenerator = self.viz_components.get('GanttGenerator')

            if not MissileManager:
                logger.error("❌ MissileManager组件不可用")
                return False
            if not VisibilityCalculator:
                logger.error("❌ VisibilityCalculator组件不可用")
                return False
            if not GanttGenerator:
                logger.warning("⚠️ GanttGenerator组件不可用，可视化功能可能受限")

            # 初始化组件
            self.missile_manager = MissileManager(self.stk_manager, self.config_manager.get_config(), self.output_manager)
            self.visibility_calculator = VisibilityCalculator(self.stk_manager)
            if GanttGenerator:
                self.gantt_generator = GanttGenerator(self.output_manager)
            else:
                self.gantt_generator = None
            
            logger.info("✅ 系统初始化成功")

            # 🔧 基于成功经验：简化设置逻辑
            logger.info("🚨 基于成功经验：检查是否需要创建Walker星座")

            # 现有项目检测已移至STK管理器的连接阶段
            logger.info("✅ STK连接成功，现有项目检测已完成")

            return True
            
        except Exception as e:
            logger.error(f"❌ 系统初始化失败: {e}")
            return False
    
    def create_walker_constellation(self) -> bool:
        """创建Walker星座"""
        try:
            logger.info("🌟 创建Walker星座...")



            # 🔧 基于成功经验：使用STK管理器的成功方法
            logger.info("🚀 开始创建Walker星座...")
            success = self.stk_manager.create_walker_constellation(self.config_manager.get_config())

            if success:
                # 获取创建的卫星
                satellites = self.stk_manager.get_objects("Satellite")
                logger.info(f"✅ Walker星座创建成功: {len(satellites)} 颗卫星")

                # 基于成功经验：验证传播状态
                logger.info("🔍 验证Walker星座传播状态...")
                if self.stk_manager._debug_verify_propagation_state():
                    logger.info("✅ Walker星座传播状态验证成功")
                else:
                    logger.warning("⚠️ Walker星座传播状态验证失败，但不影响继续执行")
                
                # 存储卫星信息
                for satellite_path in satellites:
                    satellite_id = satellite_path.split('/')[-1]
                    try:
                        satellite = self.stk_manager.scenario.Children.Item(satellite_id)
                        self.satellites[satellite_id] = {
                            "stk_object": satellite,
                            "type": "walker_constellation",
                            "created_time": datetime.now().isoformat()
                        }
                        logger.info(f"   ✅ 卫星注册成功: {satellite_id}")
                    except Exception as e:
                        logger.warning(f"   ⚠️  卫星注册失败 {satellite_id}: {e}")
                
                return True
            else:
                logger.error("❌ Walker星座创建失败")
                return False
            
        except Exception as e:
            logger.error(f"❌ Walker星座创建异常: {e}")
            return False
    
    def add_sensors(self) -> bool:
        """添加传感器载荷"""
        try:
            logger.info("📡 添加传感器载荷...")



            # 🔧 基于成功经验：传感器已在Walker星座创建时统一创建
            logger.info("📡 传感器已在Walker星座创建时统一创建")

            # 验证传感器创建状态
            sensors = self.stk_manager.get_objects("Sensor")
            logger.info(f"✅ 传感器验证成功: {len(sensors)} 个传感器")
            return True
                
        except Exception as e:
            logger.error(f"❌ 传感器添加失败: {e}")
            raise
    

    
    async def create_adk_agents(self) -> bool:
        """创建ADK智能体 - 强制使用真正的ADK框架"""
        try:
            logger.info("🤖 创建真正的ADK智能体 - 强制使用ADK框架...")

            # 首先尝试使用真正的ADK智能体
            try:
                return await self._create_real_adk_agents()
            except Exception as e:
                logger.error(f"❌ 真正的ADK智能体创建失败: {e}")
                logger.info("🔄 尝试使用卫星智能体映射器...")
                return await self._create_adk_agents_with_mapper()

        except Exception as e:
            logger.error(f"❌ 所有ADK智能体创建方法都失败: {e}")
            return False

    async def _create_real_adk_agents(self) -> bool:
        """创建真正的ADK智能体"""
        try:
            logger.info("🚀 创建真正的ADK智能体...")

            successful_agents = 0

            for satellite_name in self.satellites.keys():
                try:
                    # 构建智能体配置
                    agent_config = {
                        "satellite_id": satellite_name,
                        "capabilities": ["icbm_detection", "coordination", "tracking", "leadership"],
                        "tracking_mode": "continuous",
                        "coordination_enabled": True,
                        "constellation_config": self.config_manager.get_constellation_config(),
                        "payload_config": self.config_manager.get_payload_config(),
                        "resources": {
                            "energy": 100.0,
                            "storage": 100.0,
                            "health": 100.0,
                            "bandwidth": 25.0
                        }
                    }

                    # 创建真正的ADK智能体
                    logger.info(f"🤖 创建真正的ADK智能体: {satellite_name}")
                    # 获取真正的ADK智能体类
                    RealADKSatelliteAgent = self.agent_components.get('RealADKSatelliteAgent')
                    if not RealADKSatelliteAgent:
                        logger.error(f"❌ RealADKSatelliteAgent组件不可用")
                        continue

                    agent = RealADKSatelliteAgent(
                        satellite_id=satellite_name,
                        config=agent_config,
                        stk_manager=self.stk_manager,
                        visibility_calculator=self.visibility_calculator,
                        output_manager=self.output_manager
                    )

                    if agent:
                        self.agents[satellite_name] = agent
                        self.adk_agents[satellite_name] = agent

                        # 获取智能体状态
                        status = agent.get_agent_status()

                        logger.info(f"   ✅ 真正的ADK智能体创建成功: {satellite_name}")
                        logger.info(f"      框架: {status.get('framework', 'Unknown')}")
                        logger.info(f"      状态: {status.get('status', 'Unknown')}")
                        logger.info(f"      ADK智能体: {status.get('has_adk_agent', False)}")
                        logger.info(f"      会话: {status.get('has_session', False)}")
                        logger.info(f"      记忆服务: {status.get('has_memory_service', False)}")

                        successful_agents += 1
                    else:
                        logger.error(f"   ❌ 真正的ADK智能体创建失败: {satellite_name}")

                except Exception as e:
                    logger.error(f"   ❌ ADK智能体创建异常 {satellite_name}: {e}")

            # 创建元任务协调器
            if successful_agents > 0:
                try:
                    logger.info("🎯 创建元任务协调器...")
                    from src.adk_framework.meta_task_coordinator import MetaTaskCoordinator

                    self.meta_task_coordinator = MetaTaskCoordinator(
                        stk_manager=self.stk_manager
                    )
                    logger.info("✅ 元任务协调器创建成功")
                except Exception as coordinator_error:
                    logger.error(f"❌ 元任务协调器创建失败: {coordinator_error}")
                    # 不影响整体成功，继续执行

            logger.info(f"🎉 真正的ADK智能体创建完成:")
            logger.info(f"   总卫星数: {len(self.satellites)}")
            logger.info(f"   成功创建: {successful_agents}")
            logger.info(f"   成功率: {(successful_agents/len(self.satellites)*100):.1f}%")
            logger.info(f"   框架: 真正的ADK框架")

            return successful_agents > 0

        except Exception as e:
            logger.error(f"❌ 真正的ADK智能体创建失败: {e}")
            raise e

    async def _create_adk_agents_with_mapper(self) -> bool:
        """使用卫星智能体映射器创建ADK智能体"""
        try:
            logger.info("🗺️  使用卫星智能体映射器创建ADK智能体...")

            # 导入新的ADK框架组件
            from src.adk_framework.satellite_agent_mapper import SatelliteAgentMapper
            from src.adk_framework.meta_task_coordinator import MetaTaskCoordinator

            # 创建卫星智能体映射器
            self.satellite_agent_mapper = SatelliteAgentMapper(
                constellation_config=self.config_manager.get_constellation_config(),
                stk_manager=self.stk_manager
            )

            # 创建元任务协调器
            self.meta_task_coordinator = MetaTaskCoordinator(
                stk_manager=self.stk_manager
            )

            # 创建卫星-智能体映射
            mapping_success = await self.satellite_agent_mapper.create_satellite_agent_mapping()

            if mapping_success:
                # 获取映射统计
                stats = self.satellite_agent_mapper.get_mapping_statistics()

                # 更新agents和adk_agents字典以保持V1.0兼容性
                all_agents = self.satellite_agent_mapper.get_all_agents()
                for agent_id, agent in all_agents.items():
                    satellite_name = getattr(agent, 'satellite_id', agent_id)
                    self.agents[satellite_name] = agent
                    self.adk_agents[satellite_name] = agent

                logger.info(f"🤖 卫星智能体映射创建完成:")
                logger.info(f"   总卫星数: {stats['total_satellites']}")
                logger.info(f"   映射智能体: {stats['mapped_agents']}")
                logger.info(f"   活跃智能体: {stats['active_agents']}")
                logger.info(f"   映射成功率: {stats.get('mapping_success_rate', 0):.1f}%")

                return True
            else:
                logger.error("❌ 卫星智能体映射创建失败")
                raise Exception("卫星智能体映射创建失败")

        except Exception as e:
            logger.error(f"❌ 卫星智能体映射器创建失败: {e}")
            raise



    def create_missile_threat(self) -> bool:
        """创建导弹威胁 - 基于配置文件的5个ICBM"""
        try:
            logger.info("🚀 创建导弹威胁...")



            # 获取导弹配置
            missiles_config = self.config_manager.get_missiles_config()
            icbm_base_config = missiles_config.get('icbm_base_config', {})
            icbm_threats = missiles_config.get('icbm_threats', [])

            logger.info(f"📊 ICBM基础配置参数:")
            logger.info(f"   最大射程: {icbm_base_config.get('max_range', 15000000)/1000:.0f} km")
            logger.info(f"   最大高度: {icbm_base_config.get('max_altitude', 1500000)/1000:.0f} km")
            logger.info(f"   飞行时间: {icbm_base_config.get('flight_time', 2400)} 秒")
            logger.info(f"   导弹数量: {len(icbm_threats)} 个")

            # 创建所有ICBM威胁
            created_missiles = 0
            for i, threat_config in enumerate(icbm_threats, 1):
                try:
                    logger.info(f"\n🚀 创建第{i}个ICBM: {threat_config['missile_id']}")
                    logger.info(f"   描述: {threat_config['description']}")
                    logger.info(f"   威胁等级: {threat_config['threat_level']}")

                    # 🕐 合并基础配置和具体威胁配置 - 使用统一时间管理
                    missile_scenario = {
                        "missile_id": threat_config["missile_id"],
                        "missile_type": icbm_base_config.get("missile_type", "ballistic_missile"),
                        "description": threat_config["description"],
                        "launch_position": threat_config["launch_position"],
                        "target_position": threat_config["target_position"],
                        "launch_sequence": threat_config["launch_sequence"],  # 🕐 使用发射序号而不是偏移时间
                        "threat_level": threat_config["threat_level"]
                    }

                    # 使用导弹管理器创建导弹
                    missile_info = self.missile_manager.create_single_missile_target(missile_scenario)

                    if missile_info:
                        self.missiles[threat_config["missile_id"]] = missile_info
                        logger.info(f"   ✅ {threat_config['missile_id']} 创建成功")
                        created_missiles += 1

                        # 记录详细的导弹参数信息
                        self._log_missile_creation_summary(missile_info)
                    else:
                        logger.error(f"   ❌ {threat_config['missile_id']} 创建失败")

                except Exception as missile_error:
                    logger.error(f"   ❌ 创建导弹 {threat_config.get('missile_id', 'Unknown')} 失败: {missile_error}")

            logger.info(f"\n🚀 导弹威胁创建完成:")
            logger.info(f"   总配置数量: {len(icbm_threats)}")
            logger.info(f"   成功创建: {created_missiles}")
            logger.info(f"   创建成功率: {created_missiles/len(icbm_threats)*100:.1f}%")

            return created_missiles > 0

        except Exception as e:
            logger.error(f"❌ 导弹威胁创建失败: {e}")
            return False

    def _load_existing_constellation(self) -> bool:
        """加载现有的Walker星座"""
        try:
            logger.info("🔍 加载现有Walker星座...")

            # 获取现有卫星
            satellites = self.stk_manager.get_objects("Satellite")
            logger.info(f"📊 发现现有卫星: {len(satellites)} 颗")

            # 注册现有卫星
            for satellite_path in satellites:
                satellite_id = satellite_path.split('/')[-1]
                try:
                    satellite = self.stk_manager.scenario.Children.Item(satellite_id)
                    self.satellites[satellite_id] = {
                        "stk_object": satellite,
                        "type": "existing_constellation",
                        "loaded_time": datetime.now().isoformat()
                    }
                    logger.info(f"   ✅ 现有卫星加载: {satellite_id}")
                except Exception as e:
                    logger.warning(f"   ⚠️  卫星加载失败 {satellite_id}: {e}")

            logger.info(f"✅ 现有Walker星座加载完成: {len(self.satellites)} 颗卫星")
            return len(self.satellites) > 0

        except Exception as e:
            logger.error(f"❌ 加载现有星座失败: {e}")
            return False

    def _load_existing_sensors(self) -> bool:
        """加载现有的传感器"""
        try:
            logger.info("🔍 加载现有传感器...")

            # 获取现有传感器
            sensors = self.stk_manager.get_objects("Sensor")
            logger.info(f"📊 发现现有传感器: {len(sensors)} 个")

            # 对于现有项目，即使传感器数量为0也认为是成功的
            # 因为传感器可能已经集成在卫星中或者不需要独立的传感器对象
            logger.info(f"✅ 现有传感器验证完成: {len(sensors)} 个传感器")
            logger.info("✅ 现有项目传感器加载成功 - 传感器可能已集成在卫星中")
            return True

        except Exception as e:
            logger.error(f"❌ 加载现有传感器失败: {e}")
            return False

    def _load_existing_missiles(self) -> bool:
        """加载现有的导弹威胁"""
        try:
            logger.info("🔍 加载现有导弹威胁...")

            # 获取现有导弹
            missiles = self.stk_manager.get_objects("Missile")
            logger.info(f"📊 发现现有导弹: {len(missiles)} 个")

            missiles_loaded = 0
            # 注册现有导弹
            for missile_path in missiles:
                missile_id = missile_path.split('/')[-1]
                try:
                    missile = self.stk_manager.scenario.Children.Item(missile_id)

                    # 创建导弹信息 - 包含必要的字段
                    missile_info = {
                        "missile_id": missile_id,
                        "stk_object": missile,
                        "type": "existing_missile",
                        "loaded_time": datetime.now().isoformat(),
                        "loaded_from_existing": True,
                        "missile_type": "ballistic_missile",
                        "threat_level": "高",
                        "description": f"现有导弹威胁 {missile_id}",  # 添加description字段
                        "launch_position": {"lat": 0.0, "lon": 0.0, "alt": 0.0},
                        "target_position": {"lat": 0.0, "lon": 0.0, "alt": 0.0},
                        "launch_time_offset": self._get_missile_launch_time_offset(missile_id)
                    }

                    # 尝试获取真实轨迹信息
                    try:
                        real_trajectory = self._get_missile_real_trajectory(missile, missile_id)
                        if real_trajectory:
                            missile_info.update(real_trajectory)
                            logger.info(f"   ✅ 获取真实轨迹: {missile_id}")
                            logger.info(f"      发射位置: ({real_trajectory['launch_position']['lat']:.6f}°, {real_trajectory['launch_position']['lon']:.6f}°)")
                            logger.info(f"      目标位置: ({real_trajectory['target_position']['lat']:.6f}°, {real_trajectory['target_position']['lon']:.6f}°)")
                    except Exception as traj_error:
                        logger.debug(f"获取导弹 {missile_id} 真实轨迹失败: {traj_error}")

                    self.missiles[missile_id] = missile_info

                    # 同时添加到导弹管理器
                    if self.missile_manager:
                        self.missile_manager.missile_targets[missile_id] = missile_info
                        if hasattr(self.missile_manager, '_loaded_missile_ids'):
                            self.missile_manager._loaded_missile_ids.add(missile_id)

                    missiles_loaded += 1
                    logger.info(f"   ✅ 加载现有导弹: {missile_id}")
                except Exception as e:
                    logger.warning(f"   ⚠️  导弹加载失败 {missile_id}: {e}")

            logger.info(f"✅ 从现有STK项目加载了 {missiles_loaded} 个导弹目标")
            return missiles_loaded > 0

        except Exception as e:
            logger.error(f"❌ 加载现有导弹失败: {e}")
            return False

    def _log_missile_creation_summary(self, missile_info: Dict[str, Any]):
        """记录导弹创建摘要"""
        try:
            logger.info("="*80)
            logger.info("🚀 导弹威胁创建摘要")
            logger.info("="*80)
            logger.info(f"导弹ID: {missile_info['missile_id']}")
            logger.info(f"导弹类型: {missile_info['missile_type']}")
            logger.info(f"威胁等级: {missile_info['threat_level']}")
            logger.info(f"描述: {missile_info['description']}")
            logger.info("-" * 50)
            logger.info("📍 位置信息:")
            launch_pos = missile_info['launch_position']
            target_pos = missile_info['target_position']
            logger.info(f"  发射位置: ({launch_pos['lat']:.4f}°, {launch_pos['lon']:.4f}°)")
            logger.info(f"  目标位置: ({target_pos['lat']:.4f}°, {target_pos['lon']:.4f}°)")
            logger.info("-" * 50)
            logger.info("⏰ 时间信息:")
            logger.info(f"  发射时间: {missile_info['launch_time'].strftime('%Y-%m-%d %H:%M:%S')}")
            logger.info(f"  创建时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            logger.info("="*80)

        except Exception as e:
            logger.warning(f"导弹创建摘要记录失败: {e}")

    async def execute_system_coordination(self) -> Dict[str, Any]:
        """执行系统协调"""
        try:
            logger.info("🎯 执行系统协调...")

            if not self.satellites or not self.missiles:
                return {"error": "没有足够的卫星或导弹"}

            missile_ids = list(self.missiles.keys())
            satellite_ids = list(self.satellites.keys())

            logger.info(f"🎯 目标导弹数量: {len(missile_ids)}")
            logger.info(f"🌟 Walker星座: {len(satellite_ids)} 颗卫星")

            # 显示所有导弹信息
            for i, missile_id in enumerate(missile_ids, 1):
                missile_info = self.missiles[missile_id]
                description = missile_info.get('description', f'导弹威胁 {missile_id}')
                logger.info(f"   导弹{i}: {missile_id} ({description})")

            # 1. 计算星座可见性 - 使用优化方法
            logger.info("🔍 计算星座可见性（优化版本）...")

            # 生成原子任务信息
            original_tasks = {}
            for missile_id in missile_ids:
                try:
                    # 使用导弹管理器生成原任务信息
                    original_task_info = self.missile_manager.generate_original_task_info(missile_id)
                    if original_task_info:
                        original_tasks[missile_id] = original_task_info
                        logger.info(f"✅ 目标 {missile_id} 原任务信息生成成功")
                    else:
                        logger.warning(f"⚠️  目标 {missile_id} 原任务信息生成失败")
                except Exception as e:
                    logger.error(f"❌ 目标 {missile_id} 原任务信息生成异常: {e}")

            # 获取第一个导弹的原子任务信息作为示例
            missile_atomic_tasks = []
            if missile_ids and missile_ids[0] in original_tasks:
                tracking_task = original_tasks[missile_ids[0]].get("tracking_task", {})
                missile_atomic_tasks = tracking_task.get("atomic_tasks", [])

            # 使用优化的可见性计算 - 使用第一个导弹作为示例
            target_missile_id = missile_ids[0] if missile_ids else "ICBM_Threat_01"
            optimized_result = self.visibility_calculator.calculate_optimized_constellation_visibility(
                satellite_ids,
                target_missile_id,
                missile_atomic_tasks
            )

            # 转换为兼容格式
            constellation_result = {
                'successful_calculations': optimized_result.get('constellation_size', 0),
                'satellites_with_access': [sat_id for sat_id, data in optimized_result.get('constellation_visibility', {}).items() if data.get('has_access', False)],
                'total_access_intervals': optimized_result.get('total_access_intervals', 0),
                'satellite_results': optimized_result.get('constellation_visibility', {}),
                'meta_task_analysis': optimized_result.get('meta_task_analysis', {}),
                'optimization_stats': optimized_result.get('optimization_stats', {})
            }

            logger.info(f"   📊 优化星座可见性结果:")
            logger.info(f"      成功计算: {constellation_result['successful_calculations']}")
            logger.info(f"      有访问的卫星: {len(constellation_result['satellites_with_access'])}")
            logger.info(f"      总访问间隔: {constellation_result['total_access_intervals']}")

            # 记录优化统计信息
            if constellation_result['optimization_stats']:
                stats = constellation_result['optimization_stats']
                logger.info(f"   🚀 优化统计:")
                logger.info(f"      STK API调用次数: {stats.get('stk_api_calls', 0)}")
                logger.info(f"      原子任务分析数: {stats.get('atomic_tasks_analyzed', 0)}")
                logger.info(f"      计算效率: {stats.get('computation_efficiency', 'N/A')}")

            # 记录元任务可见性分析
            if constellation_result['meta_task_analysis']:
                logger.info(f"   📊 元任务可见性分析:")
                for sat_id, analysis in constellation_result['meta_task_analysis'].items():
                    if analysis.get('visible_tasks', 0) > 0:
                        logger.info(f"      {sat_id}: {analysis.get('visible_tasks', 0)}/{analysis.get('total_tasks', 0)} 任务可见 ({analysis.get('visibility_rate', 0):.1f}%)")

            # 生成单目标可见性甘特图
            logger.info(f"🎨 生成单目标可见性甘特图: {missile_id}")
            try:
                chart_path = self._generate_single_target_visibility_chart(
                    missile_id,
                    original_tasks.get(missile_id, {}),
                    constellation_result
                )

                if chart_path:
                    logger.info(f"   ✅ 单目标可见性甘特图生成成功: {chart_path}")
                else:
                    logger.warning(f"   ⚠️  单目标可见性甘特图生成失败")

            except Exception as viz_error:
                logger.warning(f"   ⚠️  单目标可见性甘特图生成异常: {viz_error}")

            # 2. 生成甘特图
            gantt_path = None
            if constellation_result['satellites_with_access']:
                logger.info("🎨 生成可见性甘特图...")
                try:
                    # 准备甘特图数据
                    gantt_data = {}
                    for satellite_id, result in constellation_result['satellite_results'].items():
                        if result['success'] and result['has_access']:
                            gantt_data[satellite_id] = {
                                missile_id: {
                                    'windows': result['access_intervals'],
                                    'total_intervals': result['total_intervals']
                                }
                            }

                    gantt_path = self.gantt_generator.generate_constellation_visibility_gantt(
                        missile_id, gantt_data
                    )

                    if gantt_path:
                        logger.info(f"   ✅ 甘特图生成成功: {gantt_path}")

                        if os.path.exists(gantt_path):
                            file_size = os.path.getsize(gantt_path)
                            logger.info(f"   📊 文件大小: {file_size:,} 字节")
                    else:
                        logger.warning("   ❌ 甘特图生成失败")

                except Exception as e:
                    logger.error(f"   ❌ 甘特图生成异常: {e}")

            # 3. 执行ADK多智能体协调
            adk_result = {"total_agents": 0, "successful_tasks": 0, "failed_tasks": 0}

            if self.agents:
                logger.info("🤖 执行ADK多智能体协调...")
                tracking_tasks = []

                for satellite_id, agent in self.agents.items():
                    task_config = {
                        "priority": "high",
                        "tracking_mode": "coordination",
                        "coordination_enabled": True,
                        "missile_id": missile_id,
                        "constellation_config": self.config_manager.get_constellation_config()
                    }
                    task = agent.process_missile_tracking_task(missile_id, task_config)
                    tracking_tasks.append(task)

                # 等待所有任务完成
                results = await asyncio.gather(*tracking_tasks, return_exceptions=True)

                # 分析结果
                for i, result in enumerate(results):
                    satellite_id = list(self.agents.keys())[i]

                    if isinstance(result, Exception):
                        adk_result["failed_tasks"] += 1
                        logger.warning(f"   ❌ {satellite_id}: 任务异常")
                    elif result.get("error"):
                        adk_result["failed_tasks"] += 1
                        logger.warning(f"   ❌ {satellite_id}: 任务错误")
                    else:
                        adk_result["successful_tasks"] += 1
                        logger.info(f"   ✅ {satellite_id}: 任务成功")

                adk_result["total_agents"] = len(self.agents)
                adk_result["success_rate"] = adk_result["successful_tasks"] / adk_result["total_agents"]

            # 4. 创建系统结果
            system_result = {
                "missile_id": missile_id,
                "constellation_name": self.config_manager.get_constellation_config()['name'],
                "satellites_count": len(satellite_ids),
                "constellation_result": constellation_result,
                "gantt_chart": {
                    "generated": gantt_path is not None,
                    "path": gantt_path,
                    "file_size": os.path.getsize(gantt_path) if gantt_path and os.path.exists(gantt_path) else 0
                },
                "adk_coordination": adk_result,
                "output_directory": self.output_manager.session_dir,
                "completion_time": datetime.now().isoformat()
            }

            return system_result

        except Exception as e:
            logger.error(f"❌ 系统协调执行失败: {e}")
            return {"error": str(e)}

    async def run_system(self) -> Dict[str, Any]:
        """运行完整系统"""
        try:
            logger.info("🧪 开始运行ICBM预警系统...")

            system_results = {
                "start_time": datetime.now().isoformat(),
                "phases": {},
                "overall_success": False
            }

            # 阶段1: 配置加载
            logger.info("\n📋 阶段1: 配置文件加载")
            phase1 = self.load_configuration()
            system_results["phases"]["configuration_loading"] = phase1

            if not phase1:
                return system_results

            # 阶段2: 系统初始化
            logger.info("\n📋 阶段2: 系统初始化")
            phase2 = await self.initialize_system()
            system_results["phases"]["system_initialization"] = phase2

            if not phase2:
                return system_results

            # 阶段3: Walker星座创建 (检测现有项目)
            logger.info("\n📋 阶段3: Walker星座创建")
            if self.stk_manager.skip_creation:
                logger.info("🔍 检测到现有STK项目，跳过Walker星座创建")
                phase3 = self._load_existing_constellation()
                logger.info(f"✅ 从现有项目加载Walker星座: {phase3}")
            else:
                logger.info("🆕 创建新的Walker星座")
                phase3 = self.create_walker_constellation()

            system_results["phases"]["walker_constellation_creation"] = phase3

            if not phase3:
                return system_results

            # 阶段4: 传感器添加 (检测现有项目)
            logger.info("\n📋 阶段4: 传感器添加")
            if self.stk_manager.skip_creation:
                logger.info("🔍 检测到现有STK项目，跳过传感器创建")
                phase4 = self._load_existing_sensors()
                logger.info(f"✅ 从现有项目加载传感器: {phase4}")
            else:
                logger.info("🆕 创建新的传感器")
                phase4 = self.add_sensors()

            system_results["phases"]["sensor_addition"] = phase4

            # 阶段5: ADK智能体创建
            logger.info("\n📋 阶段5: ADK智能体创建")
            phase5 = await self.create_adk_agents()
            system_results["phases"]["adk_agents_creation"] = phase5

            # 阶段6: 导弹威胁创建 (检测现有项目)
            logger.info("\n📋 阶段6: 导弹威胁创建")
            if self.stk_manager.skip_creation:
                logger.info("🔍 检测到现有STK项目，跳过导弹威胁创建")
                phase6 = self._load_existing_missiles()
                logger.info(f"✅ 从现有项目加载导弹威胁: {phase6}")
            else:
                logger.info("🆕 创建新的导弹威胁")
                phase6 = self.create_missile_threat()

            system_results["phases"]["missile_threat_creation"] = phase6

            # 对于现有项目，即使导弹威胁创建失败也继续执行后续阶段
            if not phase6 and not self.stk_manager.skip_creation:
                logger.error("❌ 导弹威胁创建失败，终止系统运行")
                return system_results
            elif not phase6:
                logger.info("⚠️  导弹威胁创建失败，但检测到现有项目，继续执行后续阶段")

            # 阶段7: 系统协调 - 基于项目成功经验
            logger.info("\n📋 阶段7: 系统协调")
            coordination_result = await self.execute_system_coordination()
            phase7 = not coordination_result.get("error")
            system_results["phases"]["system_coordination"] = phase7
            system_results["coordination_result"] = coordination_result

            # 阶段8: 目标获取和原子任务生成 - 基于项目成功经验
            logger.info("\n📋 阶段8: 目标获取和原子任务生成")
            phase8 = await self.execute_target_acquisition_and_atomic_tasks()
            system_results["phases"]["target_acquisition_atomic_tasks"] = phase8

            # 计算总体成功率 - 处理None值
            phase_values = system_results["phases"].values()
            # 将None值转换为False，True保持为True，False保持为False
            boolean_values = [bool(v) if v is not None else False for v in phase_values]
            successful_phases = sum(boolean_values)
            total_phases = len(system_results["phases"])
            system_results["overall_success"] = successful_phases == total_phases
            system_results["success_rate"] = successful_phases / total_phases if total_phases > 0 else 0
            # 使用仿真时间而不是系统时间
            from src.utils.time_manager import get_time_manager
            time_manager = get_time_manager()
            system_results["end_time"] = time_manager.start_time.isoformat()

            return system_results

        except Exception as e:
            logger.error(f"❌ 系统运行失败: {e}")
            system_results["error"] = str(e)
            # 使用仿真时间而不是系统时间
            from src.utils.time_manager import get_time_manager
            time_manager = get_time_manager()
            system_results["end_time"] = time_manager.start_time.isoformat()
            return system_results

    async def execute_target_acquisition_and_atomic_tasks(self) -> bool:
        """执行目标获取和原子任务生成 - 使用元任务协调器"""
        try:
            logger.info("🎯 开始目标获取和原子任务生成 - 使用元任务协调器...")

            # 1. 获取所有导弹目标
            missile_ids = list(self.missiles.keys())
            logger.info(f"📊 发现 {len(missile_ids)} 个导弹目标")

            # 2. 为每个目标生成原任务信息
            original_tasks = {}
            for missile_id in missile_ids:
                try:
                    # 使用导弹管理器生成原任务信息
                    original_task_info = self.missile_manager.generate_original_task_info(missile_id)
                    if original_task_info:
                        original_tasks[missile_id] = original_task_info
                        logger.info(f"✅ 目标 {missile_id} 原任务信息生成成功")
                    else:
                        logger.warning(f"⚠️  目标 {missile_id} 原任务信息生成失败")
                except Exception as e:
                    logger.error(f"❌ 目标 {missile_id} 原任务信息生成异常: {e}")

            # 3. 使用元任务协调器进行目标分配
            assignments = []

            # 检查是否有元任务协调器
            if hasattr(self, 'meta_task_coordinator') and self.meta_task_coordinator:
                logger.info("🎯 使用元任务协调器进行目标分配")

                for missile_id in missile_ids:
                    if missile_id in original_tasks:
                        try:
                            # 使用元任务协调器进行目标分配
                            assignment_result = await self.meta_task_coordinator.coordinate_target_assignment(
                                original_tasks[missile_id], self.adk_agents
                            )
                            assignments.append(assignment_result)

                            if assignment_result.get("success"):
                                logger.info(f"✅ 目标 {missile_id} 分配成功 -> {assignment_result.get('assigned_to')}")
                            else:
                                logger.warning(f"⚠️  目标 {missile_id} 分配失败: {assignment_result.get('error')}")

                        except Exception as e:
                            logger.error(f"❌ 目标 {missile_id} 协调分配异常: {e}")

            else:
                logger.error("❌ 元任务协调器不可用")
                raise Exception("元任务协调器不可用")
            # 4. 生成原任务可视化
            await self._generate_original_task_visualizations(original_tasks)

            # 5. 生成多目标原子任务对齐可视化
            await self._generate_multi_target_visualization(original_tasks)

            logger.info(f"🎯 目标获取和原子任务生成完成: {len(assignments)} 个分配")
            return len(assignments) > 0

        except Exception as e:
            logger.error(f"❌ 目标获取和原子任务生成失败: {e}")
            return False



    async def _generate_original_task_visualizations(self, original_tasks: Dict[str, Any]):
        """生成多目标对齐时间点可视化 - 只输出对齐时间点的可视化文件"""
        try:
            if not original_tasks:
                logger.info("没有原任务信息需要可视化")
                return

            logger.info("📊 生成多目标对齐时间点可视化...")
            print("\n📊 生成多目标对齐时间点可视化...")

            # 导入多目标对齐可视化器
            from src.visualization.multi_target_atomic_task_visualizer import MultiTargetAtomicTaskVisualizer

            visualizer = MultiTargetAtomicTaskVisualizer()

            try:
                # 生成多目标对齐时间点甘特图 - 使用输出管理器
                save_path = visualizer.create_multi_target_aligned_chart(original_tasks, self.output_manager)

                if save_path:
                    logger.info(f"多目标对齐时间点甘特图已生成: {save_path}")
                    print(f"📊 多目标对齐时间点甘特图已生成: {save_path}")

                    # 显示对齐时间点详细信息
                    print(f"   包含目标: {', '.join(original_tasks.keys())}")
                    print(f"   目标数量: {len(original_tasks)}个")

                    # 显示时间对齐信息
                    self._display_alignment_info(original_tasks)
                else:
                    print(f"❌ 多目标对齐时间点甘特图生成失败")

            except Exception as e:
                logger.error(f"生成多目标对齐时间点甘特图失败: {e}")
                print(f"❌ 生成多目标对齐时间点甘特图失败: {e}")

            # 关闭可视化器
            visualizer.close()

            visualizer.close()

        except Exception as e:
            logger.error(f"生成原任务可视化失败: {e}")

    async def _generate_multi_target_visualization(self, original_tasks: Dict[str, Any]):
        """生成多目标原子任务对齐可视化 - 基于项目成功经验"""
        try:
            if len(original_tasks) < 2:
                logger.info("目标数量不足，跳过多目标可视化")
                return

            logger.info("生成多目标原子任务对齐可视化...")
            print("\n📊 生成多目标原子任务对齐可视化...")

            # 使用导弹管理器生成多目标可视化 - 基于项目成功经验
            target_ids = list(original_tasks.keys())
            save_path = self.missile_manager.generate_multi_target_visualization(target_ids)

            if save_path:
                logger.info(f"多目标对齐甘特图已生成: {save_path}")
                print(f"✅ 多目标对齐甘特图已生成: {save_path}")
                print(f"   包含目标: {', '.join(target_ids)}")
                print(f"   目标数量: {len(target_ids)}个")

                # 显示时间对齐信息
                self._display_alignment_info(original_tasks)
            else:
                print("❌ 多目标可视化生成失败")

            # 生成多目标元任务分解可视化 - 严格按照标准图片样式
            await self._generate_multi_target_meta_task_visualization(original_tasks)

        except Exception as e:
            logger.error(f"生成多目标可视化失败: {e}")
            print(f"❌ 生成多目标可视化失败: {e}")

    async def _generate_multi_target_meta_task_visualization(self, original_tasks: Dict[str, Any]):
        """生成多目标元任务分解可视化 - 严格按照标准图片样式"""
        try:
            if len(original_tasks) < 2:
                logger.info("目标数量不足，跳过多目标元任务分解可视化")
                return

            logger.info("🎨 生成多目标元任务分解可视化...")
            print("\n🎨 生成多目标元任务分解可视化...")

            # 确保导弹管理器有original_tasks属性
            if not hasattr(self.missile_manager, 'original_tasks'):
                self.missile_manager.original_tasks = {}

            # 将原任务信息传递给导弹管理器
            for target_id, task_info in original_tasks.items():
                self.missile_manager.original_tasks[target_id] = task_info

            # 使用导弹管理器生成多目标元任务分解可视化
            target_ids = list(original_tasks.keys())
            save_path = self.missile_manager.generate_multi_target_meta_task_visualization(target_ids)

            if save_path:
                logger.info(f"✅ 多目标元任务分解甘特图已生成: {save_path}")
                print(f"✅ 多目标元任务分解甘特图已生成: {save_path}")
                print(f"   📋 包含目标: {', '.join(target_ids)}")
                print(f"   📊 目标数量: {len(target_ids)}个")
                print(f"   🎯 图表样式: 严格按照标准图片样式")
                print(f"   ⏰ 时间轴: st_m → end_n")
                print(f"   🔢 原子任务序号: 1-20 + δt")

                # 显示元任务分解详细信息
                self._display_meta_task_info(original_tasks)
            else:
                print("❌ 多目标元任务分解可视化生成失败")

        except Exception as e:
            logger.error(f"❌ 生成多目标元任务分解可视化失败: {e}")
            print(f"❌ 生成多目标元任务分解可视化失败: {e}")

    def _display_meta_task_info(self, original_tasks: Dict[str, Any]):
        """显示元任务分解详细信息"""
        try:
            print(f"\n📋 元任务分解详细信息:")

            for i, (target_id, task_info) in enumerate(original_tasks.items(), 1):
                tracking_task = task_info.get("tracking_task", {})
                atomic_tasks = task_info.get("atomic_tasks", [])

                if tracking_task:
                    start_time = tracking_task.get("start_time")
                    end_time = tracking_task.get("end_time")
                    duration = tracking_task.get("duration", 0)

                    print(f"   Task{i} ({target_id}):")
                    print(f"     开始时间: st{i} = {start_time}")
                    print(f"     结束时间: end{i} = {end_time}")
                    print(f"     持续时间: {duration:.1f}秒")
                    print(f"     原子任务数: {len(atomic_tasks)}个")

            print(f"   🔢 原子任务序号: 1-{len(atomic_tasks)} + δt")
            print(f"   ⏰ 全局时间轴: st_m → end_n")

        except Exception as e:
            logger.error(f"显示元任务分解信息失败: {e}")

    def _display_alignment_info(self, original_tasks: Dict[str, Any]):
        """显示时间对齐信息 - 基于项目成功经验"""
        try:
            print(f"\n📋 时间对齐详细信息:")
            all_start_times = []
            all_end_times = []

            for target_id, task_info in original_tasks.items():
                tracking_task = task_info.get("tracking_task", {})
                if tracking_task:
                    all_start_times.append(tracking_task["start_time"])
                    all_end_times.append(tracking_task["end_time"])

                    print(f"   目标 {target_id}:")
                    print(f"     开始时间: {tracking_task['start_time']}")
                    print(f"     结束时间: {tracking_task['end_time']}")
                    print(f"     持续时间: {tracking_task.get('duration', 0):.1f}秒")

            if all_start_times and all_end_times:
                global_start = min(all_start_times)
                global_end = max(all_end_times)
                global_duration = (global_end - global_start).total_seconds()

                print(f"\n📊 全局时间信息:")
                print(f"   全局开始时间: {global_start}")
                print(f"   全局结束时间: {global_end}")
                print(f"   全局持续时间: {global_duration:.1f}秒")

        except Exception as e:
            logger.error(f"显示时间对齐信息失败: {e}")

    def _estimate_satellite_position(self, satellite_id: str) -> Dict[str, float]:
        """估算卫星位置 - 基于项目成功经验"""
        try:
            # 基于Walker星座配置估算卫星位置
            constellation_config = self.config.get("constellation", {})
            num_planes = constellation_config.get("planes", 3)
            sats_per_plane = constellation_config.get("satellites_per_plane", 3)

            # 解析卫星编号
            import re
            match = re.search(r'(\d+)', satellite_id)
            if match:
                sat_number = int(match.group(1))

                # 计算轨道面和卫星在面内的位置
                plane_index = (sat_number - 1) // sats_per_plane
                sat_in_plane = (sat_number - 1) % sats_per_plane

                # 计算RAAN和真近点角
                raan_spacing = 360.0 / num_planes
                raan = plane_index * raan_spacing

                true_anomaly_spacing = 360.0 / sats_per_plane
                true_anomaly = sat_in_plane * true_anomaly_spacing

                # 简化的轨道位置计算（圆形轨道）
                import math
                ref_sat = constellation_config.get("reference_satellite", {})
                altitude = ref_sat.get("altitude", 1800)  # km
                inclination = ref_sat.get("inclination", 51.856)  # degrees

                # 基于仿真时间的轨道位置估算
                from src.utils.time_manager import get_time_manager
                time_manager = get_time_manager()
                sim_time = time_manager.start_time
                time_since_epoch = (sim_time.timestamp() % 5760) / 5760 * 360  # 96分钟轨道周期
                current_true_anomaly = (true_anomaly + time_since_epoch) % 360

                # 转换为地理坐标（改进的计算）
                # 使用更准确的轨道力学计算
                inc_rad = math.radians(inclination)
                raan_rad = math.radians(raan)
                ta_rad = math.radians(current_true_anomaly)

                # 轨道坐标系中的位置
                earth_radius = 6371.0  # km
                orbit_radius = earth_radius + altitude

                # 轨道平面内的位置
                x_orbit = orbit_radius * math.cos(ta_rad)
                y_orbit = orbit_radius * math.sin(ta_rad)
                z_orbit = 0.0

                # 转换到地心坐标系
                x_eci = (x_orbit * math.cos(raan_rad) -
                        y_orbit * math.cos(inc_rad) * math.sin(raan_rad))
                y_eci = (x_orbit * math.sin(raan_rad) +
                        y_orbit * math.cos(inc_rad) * math.cos(raan_rad))
                z_eci = y_orbit * math.sin(inc_rad)

                # 转换为地理坐标
                r = math.sqrt(x_eci*x_eci + y_eci*y_eci + z_eci*z_eci)
                lat = math.degrees(math.asin(z_eci / r))
                lon = math.degrees(math.atan2(y_eci, x_eci))

                # 确保经度在-180到180度之间
                if lon > 180:
                    lon -= 360
                elif lon < -180:
                    lon += 360

                position = {
                    "lat": lat,
                    "lon": lon,
                    "alt": altitude * 1000  # 转换为米
                }

                logger.debug(f"估算卫星位置 {satellite_id}: 轨道面{plane_index+1}, 位置{sat_in_plane+1}, "
                           f"坐标({lat:.3f}°, {lon:.3f}°, {altitude:.1f}km)")
                return position
            else:
                raise Exception(f"无法确定卫星 {satellite_id} 的轨道参数")

        except Exception as e:
            logger.error(f"估算卫星位置失败: {e}")
            raise

    def _load_existing_satellites(self):
        """加载现有STK项目中的卫星信息"""
        try:
            if not self.stk_manager or not self.stk_manager.scenario:
                logger.warning("STK管理器或场景不可用")
                return

            scenario = self.stk_manager.scenario
            satellites_loaded = 0

            # 遍历场景中的所有对象
            for i in range(scenario.Children.Count):
                child = scenario.Children.Item(i)
                if child.ClassName == "Satellite":
                    satellite_name = child.InstanceName
                    self.satellites[satellite_name] = {
                        "name": satellite_name,
                        "stk_object": child,
                        "loaded_from_existing": True
                    }
                    satellites_loaded += 1
                    logger.info(f"   ✅ 加载现有卫星: {satellite_name}")

            logger.info(f"✅ 从现有STK项目加载了 {satellites_loaded} 颗卫星")

            # 🔧 基于成功经验：简化现有卫星处理
            if satellites_loaded > 0:
                logger.info("🔍 验证现有卫星状态...")
                self.stk_manager._debug_verify_propagation_state()

        except Exception as e:
            logger.error(f"❌ 加载现有卫星失败: {e}")



    def _get_missile_real_trajectory(self, missile_stk_object, missile_name: str) -> Optional[Dict[str, Any]]:
        """
        获取导弹真实轨迹信息 - 基于项目成功经验
        从STK中读取导弹的真实发射位置、目标位置和轨迹参数

        Args:
            missile_stk_object: STK导弹对象
            missile_name: 导弹名称

        Returns:
            Optional[Dict]: 真实轨迹信息或None
        """
        try:
            logger.info(f"🎯 读取导弹 {missile_name} 的真实轨迹...")

            # 1. 获取导弹轨迹对象
            try:
                trajectory = missile_stk_object.Trajectory
                logger.info(f"   ✅ 轨迹对象获取成功")
            except Exception as traj_error:
                logger.warning(f"   ⚠️  轨迹对象获取失败: {traj_error}")
                return None

            # 2. 获取发射位置 - 基于项目成功经验
            launch_position = None
            try:
                launch_lat = trajectory.Launch.Lat
                launch_lon = trajectory.Launch.Lon
                launch_alt = trajectory.Launch.Alt

                launch_position = {
                    "lat": launch_lat,
                    "lon": launch_lon,
                    "alt": launch_alt
                }
                logger.info(f"   ✅ 发射位置: ({launch_lat:.6f}°, {launch_lon:.6f}°, {launch_alt:.1f}m)")

            except Exception as launch_error:
                logger.debug(f"   获取发射位置失败: {launch_error}")

            # 3. 获取撞击位置 - 基于项目成功经验
            target_position = None
            try:
                impact_lat = trajectory.ImpactLocation.Impact.Lat
                impact_lon = trajectory.ImpactLocation.Impact.Lon
                impact_alt = trajectory.ImpactLocation.Impact.Alt

                target_position = {
                    "lat": impact_lat,
                    "lon": impact_lon,
                    "alt": impact_alt
                }
                logger.info(f"   ✅ 撞击位置: ({impact_lat:.6f}°, {impact_lon:.6f}°, {impact_alt:.1f}m)")

            except Exception as impact_error:
                logger.debug(f"   获取撞击位置失败: {impact_error}")

            # 4. 使用DataProvider获取轨迹数据 - 基于项目成功经验
            if not launch_position or not target_position:
                logger.info(f"   🔄 尝试使用DataProvider获取轨迹数据...")
                trajectory_data = self._get_trajectory_data_from_dataprovider(missile_stk_object)

                if trajectory_data:
                    if not launch_position and trajectory_data.get("launch_position"):
                        launch_position = trajectory_data["launch_position"]
                        logger.info(f"   ✅ DataProvider发射位置: ({launch_position['lat']:.6f}°, {launch_position['lon']:.6f}°)")

                    if not target_position and trajectory_data.get("target_position"):
                        target_position = trajectory_data["target_position"]
                        logger.info(f"   ✅ DataProvider撞击位置: ({target_position['lat']:.6f}°, {target_position['lon']:.6f}°)")

            # 5. 构建真实轨迹信息
            if launch_position and target_position:
                real_trajectory = {
                    "launch_position": launch_position,
                    "target_position": target_position,
                    "trajectory_source": "stk_real_data",
                    "has_real_trajectory": True
                }

                # 计算射程
                import math
                lat1, lon1 = math.radians(launch_position["lat"]), math.radians(launch_position["lon"])
                lat2, lon2 = math.radians(target_position["lat"]), math.radians(target_position["lon"])
                dlat = lat2 - lat1
                dlon = lon2 - lon1
                a = math.sin(dlat/2)**2 + math.cos(lat1) * math.cos(lat2) * math.sin(dlon/2)**2
                c = 2 * math.asin(math.sqrt(a))
                range_km = 6371 * c  # 地球半径

                real_trajectory["range_km"] = range_km
                logger.info(f"   📏 计算射程: {range_km:.1f} km")

                return real_trajectory
            else:
                logger.warning(f"   ⚠️  无法获取完整的轨迹信息")
                return None

        except Exception as e:
            logger.error(f"❌ 获取导弹真实轨迹失败: {e}")
            return None

    def _get_trajectory_data_from_dataprovider(self, missile_stk_object) -> Optional[Dict[str, Any]]:
        """
        使用DataProvider获取轨迹数据 - 基于项目成功经验

        Args:
            missile_stk_object: STK导弹对象

        Returns:
            Optional[Dict]: 轨迹数据或None
        """
        try:
            # 尝试使用LLA Position DataProvider
            try:
                lla_dp = missile_stk_object.DataProviders.Item("LLA Position")
                if lla_dp:
                    result = lla_dp.Exec()
                    if result and result.DataSets.Count > 0:
                        dataset = result.DataSets.Item(0)
                        if dataset.RowCount > 0:
                            # 获取第一个点（发射位置）
                            launch_lat = dataset.GetValue(0, 1)
                            launch_lon = dataset.GetValue(0, 2)
                            launch_alt = dataset.GetValue(0, 3) * 1000  # 转换为米

                            # 获取最后一个点（撞击位置）
                            last_row = dataset.RowCount - 1
                            target_lat = dataset.GetValue(last_row, 1)
                            target_lon = dataset.GetValue(last_row, 2)
                            target_alt = dataset.GetValue(last_row, 3) * 1000  # 转换为米

                            return {
                                "launch_position": {
                                    "lat": launch_lat,
                                    "lon": launch_lon,
                                    "alt": launch_alt
                                },
                                "target_position": {
                                    "lat": target_lat,
                                    "lon": target_lon,
                                    "alt": target_alt
                                }
                            }
            except Exception as lla_error:
                logger.debug(f"LLA Position DataProvider失败: {lla_error}")

            # 尝试使用Cartesian Position DataProvider
            try:
                cart_dp = missile_stk_object.DataProviders.Item("Cartesian Position")
                if cart_dp:
                    result = cart_dp.Exec()
                    if result and result.DataSets.Count > 0:
                        dataset = result.DataSets.Item(0)
                        if dataset.RowCount > 0:
                            # 获取第一个点（发射位置）
                            x1 = dataset.GetValue(0, 1) * 1000  # 转换为米
                            y1 = dataset.GetValue(0, 2) * 1000
                            z1 = dataset.GetValue(0, 3) * 1000

                            # 获取最后一个点（撞击位置）
                            last_row = dataset.RowCount - 1
                            x2 = dataset.GetValue(last_row, 1) * 1000
                            y2 = dataset.GetValue(last_row, 2) * 1000
                            z2 = dataset.GetValue(last_row, 3) * 1000

                            # 转换为地理坐标
                            import math

                            # 发射位置
                            r1 = math.sqrt(x1*x1 + y1*y1 + z1*z1)
                            launch_lat = math.degrees(math.asin(z1 / r1))
                            launch_lon = math.degrees(math.atan2(y1, x1))
                            launch_alt = r1 - 6371000  # 高度(米)

                            # 撞击位置
                            r2 = math.sqrt(x2*x2 + y2*y2 + z2*z2)
                            target_lat = math.degrees(math.asin(z2 / r2))
                            target_lon = math.degrees(math.atan2(y2, x2))
                            target_alt = r2 - 6371000  # 高度(米)

                            return {
                                "launch_position": {
                                    "lat": launch_lat,
                                    "lon": launch_lon,
                                    "alt": launch_alt
                                },
                                "target_position": {
                                    "lat": target_lat,
                                    "lon": target_lon,
                                    "alt": target_alt
                                }
                            }
            except Exception as cart_error:
                logger.debug(f"Cartesian Position DataProvider失败: {cart_error}")

            return None

        except Exception as e:
            logger.error(f"DataProvider获取轨迹数据失败: {e}")
            return None

    def _get_missile_launch_time_offset(self, missile_name: str) -> int:
        """
        从配置文件获取导弹发射时间偏移

        Args:
            missile_name: 导弹名称

        Returns:
            int: 发射时间偏移(秒)
        """
        try:
            if hasattr(self, 'config') and self.config:
                icbm_threats = self.config.get('missiles', {}).get('icbm_threats', [])

                for threat in icbm_threats:
                    if threat.get('missile_id') == missile_name:
                        offset = threat.get('launch_time_offset', 120)
                        logger.info(f"   📅 {missile_name} 发射时间偏移: {offset}秒")
                        return offset

                logger.error(f"   ❌ 未找到 {missile_name} 的配置")
                raise Exception(f"未找到 {missile_name} 的配置")
            else:
                logger.error(f"   ❌ 配置文件不可用")
                raise Exception("配置文件不可用")

        except Exception as e:
            logger.error(f"获取导弹发射时间偏移失败: {e}")
            raise

    def _generate_single_target_visibility_chart(self, missile_id: str,
                                               original_task_info: Dict[str, Any],
                                               constellation_result: Dict[str, Any]) -> str:
        """
        生成单目标可见性甘特图
        横坐标：时间轴
        纵坐标：卫星
        内容：基于原子任务的可见时间窗口

        Args:
            missile_id: 导弹ID
            original_task_info: 原任务信息
            constellation_result: 星座可见性计算结果

        Returns:
            str: 图表文件路径
        """
        try:
            import matplotlib.pyplot as plt
            import matplotlib.dates as mdates
            from matplotlib.patches import Rectangle
            from datetime import datetime
            import os

            logger.info(f"🎨 生成单目标可见性甘特图: {missile_id}")

            # 1. 解析原子任务信息
            atomic_tasks = original_task_info.get("atomic_tasks", [])
            tracking_task = original_task_info.get("tracking_task", {})

            if not atomic_tasks:
                logger.warning(f"   ⚠️  无原子任务信息，跳过图表生成")
                return None

            # 2. 获取有可见性的卫星
            satellites_with_access = constellation_result.get('satellites_with_access', [])
            meta_task_analysis = constellation_result.get('meta_task_analysis', {})
            satellite_results = constellation_result.get('satellite_results', {})

            if not satellites_with_access:
                logger.warning(f"   ⚠️  无可见卫星，跳过图表生成")
                return None

            # 3. 创建图表
            fig, ax = plt.subplots(figsize=(16, 10))

            # 4. 设置标题和标签
            ax.set_title(f"单目标可见性甘特图: {missile_id}\n基于原子任务的可见时间窗口", fontsize=16, fontweight='bold')
            ax.set_xlabel("时间轴", fontsize=14)
            ax.set_ylabel("卫星", fontsize=14)

            # 5. 解析时间范围
            all_times = []
            for task in atomic_tasks:
                start_time_str = task.get("start_time")
                end_time_str = task.get("end_time")

                if start_time_str and end_time_str:
                    if isinstance(start_time_str, str):
                        start_time = datetime.fromisoformat(start_time_str.replace('Z', '+00:00'))
                    else:
                        start_time = start_time_str

                    if isinstance(end_time_str, str):
                        end_time = datetime.fromisoformat(end_time_str.replace('Z', '+00:00'))
                    else:
                        end_time = end_time_str

                    all_times.extend([start_time, end_time])

            if not all_times:
                logger.warning(f"   ⚠️  无有效时间信息，跳过图表生成")
                return None

            # 6. 设置时间轴
            min_time = min(all_times)
            max_time = max(all_times)
            ax.set_xlim(min_time, max_time)

            # 设置时间格式
            ax.xaxis.set_major_formatter(mdates.DateFormatter('%H:%M:%S'))
            ax.xaxis.set_major_locator(mdates.MinuteLocator(interval=2))
            plt.xticks(rotation=45)

            # 7. 设置卫星轴
            satellites = satellites_with_access
            ax.set_ylim(-0.5, len(satellites) - 0.5)
            ax.set_yticks(range(len(satellites)))
            ax.set_yticklabels(satellites)

            # 8. 为每个卫星绘制原子任务条
            for sat_idx, satellite_id in enumerate(satellites):
                satellite_y = sat_idx

                # 获取该卫星的元任务分析结果
                sat_meta_analysis = meta_task_analysis.get(satellite_id, {})
                task_visibility_list = sat_meta_analysis.get('task_visibility', [])

                for i, task in enumerate(atomic_tasks):
                    start_time_str = task.get("start_time")
                    end_time_str = task.get("end_time")

                    if start_time_str and end_time_str:
                        if isinstance(start_time_str, str):
                            start_time = datetime.fromisoformat(start_time_str.replace('Z', '+00:00'))
                        else:
                            start_time = start_time_str

                        if isinstance(end_time_str, str):
                            end_time = datetime.fromisoformat(end_time_str.replace('Z', '+00:00'))
                        else:
                            end_time = end_time_str

                        # 判断该原子任务是否可见
                        is_visible = False
                        if i < len(task_visibility_list):
                            is_visible = task_visibility_list[i].get('is_visible', False)

                        # 设置颜色：可见=绿色，不可见=灰色
                        color = 'green' if is_visible else 'lightgray'
                        alpha = 0.8 if is_visible else 0.3

                        # 绘制任务条
                        duration_hours = (end_time - start_time).total_seconds() / 3600
                        ax.barh(satellite_y, duration_hours, left=mdates.date2num(start_time),
                               height=0.6, color=color, alpha=alpha, edgecolor='black', linewidth=0.5)

                        # 添加任务标签（只在可见任务上显示）
                        if is_visible:
                            task_label = f"T{i+1}"
                            ax.text(mdates.date2num(start_time) + duration_hours/2, satellite_y, task_label,
                                   ha='center', va='center', color='white', fontweight='bold', fontsize=8)

                # 绘制该卫星的可见窗口背景
                sat_result = satellite_results.get(satellite_id, {})
                access_intervals = sat_result.get("access_intervals", [])

                for interval in access_intervals:
                    start_str = interval.get("start")
                    end_str = interval.get("stop") or interval.get("end")

                    if start_str and end_str:
                        try:
                            # 解析STK时间格式
                            start_time = self._parse_stk_time_for_chart(start_str)
                            end_time = self._parse_stk_time_for_chart(end_str)

                            if start_time and end_time:
                                # 绘制可见窗口背景
                                rect = Rectangle(
                                    (mdates.date2num(start_time), satellite_y - 0.4),
                                    mdates.date2num(end_time) - mdates.date2num(start_time),
                                    0.8,
                                    color='skyblue', alpha=0.3, zorder=0
                                )
                                ax.add_patch(rect)
                        except Exception as parse_error:
                            logger.debug(f"时间解析失败: {parse_error}")
                            continue

            # 9. 添加图例
            ax.barh(0, 0, color='green', alpha=0.8, label='可见原子任务')
            ax.barh(0, 0, color='lightgray', alpha=0.3, label='不可见原子任务')
            ax.barh(0, 0, color='skyblue', alpha=0.3, label='STK可见窗口')
            ax.legend(loc='upper right')

            # 10. 添加统计信息
            total_satellites = len(satellites)
            total_tasks = len(atomic_tasks)
            total_visible_tasks = sum(analysis.get('visible_tasks', 0) for analysis in meta_task_analysis.values())

            plt.figtext(
                0.5, 0.02,
                f"统计信息: {total_satellites}颗卫星, {total_tasks}个原子任务, {total_visible_tasks}个可见任务",
                ha='center', fontsize=12, bbox=dict(facecolor='yellow', alpha=0.3)
            )

            # 11. 保存图表
            output_dir = self.output_manager.get_output_dir()
            filename = f"single_target_visibility_{missile_id}_constellation.png"
            output_path = os.path.join(output_dir, filename)

            plt.tight_layout()
            plt.savefig(output_path, dpi=150, bbox_inches='tight')
            plt.close()

            logger.info(f"   ✅ 单目标可见性甘特图保存成功: {output_path}")
            return output_path

        except Exception as e:
            logger.error(f"   ❌ 单目标可见性甘特图生成失败: {e}")
            return None

    def _parse_stk_time_for_chart(self, stk_time_str: str) -> datetime:
        """解析STK时间格式用于图表显示"""
        try:
            from datetime import datetime
            # STK时间格式: "14 May 2025 04:07:00.000"
            return datetime.strptime(stk_time_str, "%d %b %Y %H:%M:%S.%f")
        except:
            try:
                # 尝试其他格式
                return datetime.strptime(stk_time_str, "%d %b %Y %H:%M:%S")
            except:
                return None

async def main():
    """主函数"""
    system = ICBMWarningSystem()

    try:
        # 运行系统
        results = await system.run_system()

        # 输出结果
        print("\n" + "="*80)
        print("🎯 ICBM预警系统运行结果")
        print("="*80)
        print(f"总体成功: {'✅' if results['overall_success'] else '❌'}")
        print(f"成功率: {results.get('success_rate', 0):.1%}")

        for phase_name, phase_result in results.get("phases", {}).items():
            status = "✅" if phase_result else "❌"
            print(f"{status} {phase_name}: {phase_result}")

        if results.get("coordination_result") and not results["coordination_result"].get("error"):
            coord_result = results["coordination_result"]

            # 输出目录
            if "output_directory" in coord_result:
                print(f"\n📁 输出目录: {coord_result['output_directory']}")

            # 星座可见性结果
            if "constellation_result" in coord_result:
                const_result = coord_result["constellation_result"]
                print(f"\n🌟 星座可见性:")
                print(f"   成功计算: {const_result.get('successful_calculations', 0)}")
                print(f"   有访问的卫星: {len(const_result.get('satellites_with_access', []))}")
                print(f"   总访问间隔: {const_result.get('total_access_intervals', 0)}")

            # 甘特图结果
            if "gantt_chart" in coord_result:
                gantt_result = coord_result["gantt_chart"]
                print(f"\n🎨 甘特图:")
                print(f"   生成成功: {gantt_result.get('generated', False)}")
                if gantt_result.get('path'):
                    print(f"   文件路径: {gantt_result['path']}")
                    print(f"   文件大小: {gantt_result.get('file_size', 0):,} 字节")

            # ADK协调结果
            if "adk_coordination" in coord_result:
                adk_result = coord_result["adk_coordination"]
                print(f"\n🤖 ADK协调:")
                print(f"   总智能体: {adk_result.get('total_agents', 0)}")
                print(f"   成功任务: {adk_result.get('successful_tasks', 0)}")
                print(f"   失败任务: {adk_result.get('failed_tasks', 0)}")
                print(f"   成功率: {adk_result.get('success_rate', 0):.1%}")

        if results.get("error"):
            print(f"\n❌ 错误: {results['error']}")

        print("="*80)

        if results['overall_success']:
            print("\n🚀 ICBM预警系统运行完全成功！")
            print("   ✅ 系统完全可用")
            print("   ✅ 所有功能模块正常")
            print("   ✅ 达到生产就绪状态")
        else:
            print(f"\n⚠️  系统部分功能正常，成功率: {results.get('success_rate', 0):.1%}")

    except Exception as e:
        logger.error(f"❌ 系统运行失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
