# ICBM预警系统配置文件 v1.0

# 星座配置
constellation:
  name: "Walker_Constellation"
  type: "Walker"
  planes: 3
  satellites_per_plane: 3
  total_satellites: 9
  
  # 参考卫星轨道参数
  reference_satellite:
    altitude: 1800             # 轨道高度(km)
    inclination: 51.856        # 轨道倾角(度)
    eccentricity: 0.0          # 轨道偏心率
    arg_of_perigee: 12         # 近地点幅角(度)
    raan_offset: 24            # 升交点赤经偏移(度)
    mean_anomaly_offset: 180   # 平近点角(度)


# 载荷参数配置 - 光学传感器（优化版本）
payload:
  type: "Optical_Sensor"
  mounting: "Nadir"            # 载荷安装方向：天底方向

  # 传感器视场配置 - STK圆锥视场参数
  sensor_pattern: "Conic"      # 传感器模式：锥形
  inner_cone_half_angle: 66.1  # 内锥半角(度) - STK约束: 0.0° - 90.0°
  outer_cone_half_angle: 85.0  # 外锥半角(度) - STK约束: 0.0° - 180.0°
  clockwise_angle_min: 0.0     # 顺时针旋转角最小值(度) - 用户自定义约束
  clockwise_angle_max: 360.0   # 顺时针旋转角最大值(度) - 用户自定义约束

  # 指向参数配置 - 新增
  pointing:
    azimuth: 0.0              # 指向方位角(度)
    elevation: 90.0           # 指向俯仰角(度)

  # 传感器约束（导弹跟踪优化版本）
  constraints_range:
    min_range: 0      # 最小距离设为0，允许近距离跟踪
    max_range: 5000   # 最大距离增加到5000km，确保覆盖所有可能距离
    active: true      # 约束激活状态

  # 任务约束（新增）
  task_constraints:
    min_observation_time: 30   # 最小观测时间(秒)
    max_observation_time: 600  # 最大观测时间(秒)
    slew_rate: 2.0            # 转向速率(度/秒)
    pointing_accuracy: 0.1     # 指向精度(度)

  # 能源管理（新增）
  energy_management:
    battery_capacity: 1000     # 电池容量(Wh)
    standby_power: 20          # 待机功耗(W)
    observation_power: 80       # 观测功耗(W)
    transmission_power: 60      # 传输功耗(W)

# 导弹配置 - 5个洲际弹道导弹
missiles:
  # ICBM基本参数
  icbm_base_config:
    max_range: 15000000      # meters (15000 km)
    max_altitude: 1500000    # meters (1500 km)
    flight_time: 2400        # seconds (40 minutes)
    missile_type: "ballistic_missile"
    trajectory_type: "ballistic"

  # 5个具体的ICBM威胁场景
  icbm_threats:
    - missile_id: "ICBM_Threat_01"
      description: "俄罗斯-美国东海岸"
      launch_position:
        lat: 55.7558    # 莫斯科
        lon: 37.6176
        alt: 0.0
      target_position:
        lat: 38.9072    # 华盛顿
        lon: -77.0369
        alt: 0.0
      launch_sequence: 1         # 发射序号 (将根据simulation.missile_launch_schedule计算实际时间)
      threat_level: "极高"

    - missile_id: "ICBM_Threat_02"
      description: "俄罗斯-美国西海岸"
      launch_position:
        lat: 55.7558    # 莫斯科
        lon: 37.6176
        alt: 0.0
      target_position:
        lat: 34.0522    # 洛杉矶
        lon: -118.2437
        alt: 0.0
      launch_sequence: 2         # 发射序号
      threat_level: "极高"

    - missile_id: "ICBM_Threat_03"
      description: "朝鲜-日本"
      launch_position:
        lat: 39.0392    # 平壤
        lon: 125.7625
        alt: 0.0
      target_position:
        lat: 35.6762    # 东京
        lon: 139.6503
        alt: 0.0
      launch_sequence: 3         # 发射序号
      threat_level: "高"

    - missile_id: "ICBM_Threat_04"
      description: "中国-关岛"
      launch_position:
        lat: 39.9042    # 北京
        lon: 116.4074
        alt: 0.0
      target_position:
        lat: 13.4443    # 关岛
        lon: 144.7937
        alt: 0.0
      launch_sequence: 4         # 发射序号
      threat_level: "中"

    - missile_id: "ICBM_Threat_05"
      description: "伊朗-以色列"
      launch_position:
        lat: 35.6892    # 德黑兰
        lon: 51.3890
        alt: 0.0
      target_position:
        lat: 31.7683    # 耶路撒冷
        lon: 35.2137
        alt: 0.0
      launch_sequence: 5         # 发射序号
      threat_level: "中"

# 🕐 统一仿真时间配置 - 所有时间相关设置的唯一来源
simulation:
  # 基准时间设置 - 仿真时间配置（不依赖系统时间）
  start_time: "21 Jul 2025 04:00:00.000"    # 仿真开始时间 (STK格式)
  end_time: "22 Jul 2025 04:00:00.000"      # 仿真结束时间 (STK格式)
  epoch_time: "21 Jul 2025 04:00:00.000"    # 历元时间 (与开始时间相同)
  time_step: 60                             # 时间步长(秒)

  # 时间管理设置
  force_time_reset: true                    # 是否强制重置现有项目的时间
  auto_start_simulation: false              # 是否自动开始仿真
  time_zone: "UTC"                          # 时区设置

  # 导弹发射时间配置 (相对于仿真开始时间的偏移，单位：秒)
  missile_launch_schedule:
    base_offset: 120                        # 基础发射偏移时间 (2分钟)
    interval: 60                            # 导弹间发射间隔 (1分钟)
    max_offset: 600                         # 最大发射偏移时间 (10分钟)

  # 任务时间窗口配置
  task_timing:
    midcourse_start_offset: 300             # 中段飞行开始偏移 (发射后5分钟)
    midcourse_end_offset: 300               # 中段飞行结束偏移 (撞击前5分钟)
    min_midcourse_duration: 600             # 最小中段飞行时间 (10分钟)
    atomic_task_duration: 60                # 原子任务持续时间 (1分钟)

  # 可见性计算时间配置
  visibility_timing:
    calculation_start_offset: 0             # 可见性计算开始偏移 (相对于仿真开始)
    calculation_duration: 86400             # 可见性计算持续时间 (24小时)
    time_resolution: 60                     # 时间分辨率 (1分钟)

# STK配置
stk:
  visible: true              # STK窗口是否可见
  user_control: true         # 是否启用用户控制
  interactive: true          # 是否启用交互模式
  auto_save: false           # 是否自动保存
  startup_timeout: 10        # 启动超时时间（秒）
  # 现有项目检测配置
  detect_existing_project: true    # 是否检测现有STK项目
  existing_project_wait_time: 5    # 检测到现有项目时的等待时间(秒)

# ADK配置
adk:
  coordination_enabled: true
  tracking_mode: "continuous"
  priority: "high"

# 任务规划配置 - 基于项目成功经验
task_planning:
  atomic_task_duration: 60        # 原子任务时长(秒) - 基于项目成功经验
  planning_horizon: 3600          # 规划时域(秒)
  planning_interval: 300          # 规划间隔(秒)
  midcourse_buffer_minutes: 5     # 中段飞行缓冲时间(分钟)

  # 任务分配参数
  task_allocation:
    max_concurrent_tasks: 3       # 每颗卫星最大并发任务数
    priority_weights:
      high: 1.0
      medium: 0.7
      low: 0.4

# 输出配置
output:
  base_directory: "test_outputs"
  create_timestamp_dir: true
  save_gantt_charts: true
  save_visibility_data: true
  save_reports: true
