#!/usr/bin/env python3
"""
甘特图生成器模块
负责生成可见性甘特图
"""

import os
import logging
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional

# 设置中文字体支持
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

logger = logging.getLogger(__name__)

class GanttGenerator:
    """甘特图生成器"""
    
    def __init__(self, output_manager):
        """初始化甘特图生成器"""
        self.output_manager = output_manager
        
        logger.info("🎨 甘特图生成器初始化")
    
    def generate_constellation_visibility_gantt(self, missile_id: str, gantt_data: Dict[str, Any]) -> Optional[str]:
        """生成星座可见性甘特图"""
        try:
            logger.info(f"🎨 生成星座可见性甘特图: {missile_id}")
            
            if not gantt_data:
                logger.warning("⚠️  没有甘特图数据")
                return None
            
            # 创建图形
            fig, ax = plt.subplots(figsize=(12, 8))
            
            # 准备数据
            satellites = list(gantt_data.keys())
            y_positions = range(len(satellites))
            
            # 解析时间窗口
            all_windows = []
            for satellite_id, missile_data in gantt_data.items():
                if missile_id in missile_data:
                    windows = missile_data[missile_id].get('windows', [])
                    for window in windows:
                        try:
                            start_time = self._parse_time(window['start'])
                            stop_time = self._parse_time(window['stop'])
                            if start_time and stop_time:
                                all_windows.append({
                                    'satellite': satellite_id,
                                    'start': start_time,
                                    'stop': stop_time
                                })
                        except Exception as e:
                            logger.warning(f"⚠️  时间窗口解析失败: {e}")
            
            logger.info(f"📊 解析到 {len(all_windows)} 个可见窗口")
            
            # 绘制甘特图
            colors = plt.cm.Set3(np.linspace(0, 1, len(satellites)))
            
            for i, satellite_id in enumerate(satellites):
                satellite_windows = [w for w in all_windows if w['satellite'] == satellite_id]
                
                for window in satellite_windows:
                    duration = (window['stop'] - window['start']).total_seconds() / 60  # 分钟
                    
                    ax.barh(i, duration, left=mdates.date2num(window['start']), 
                           height=0.6, color=colors[i], alpha=0.7,
                           label=satellite_id if window == satellite_windows[0] else "")
            
            # 设置图形属性
            ax.set_yticks(y_positions)
            ax.set_yticklabels(satellites)
            ax.set_xlabel('时间')
            ax.set_ylabel('卫星')
            ax.set_title(f'卫星对导弹 {missile_id} 的可见性甘特图')
            
            # 设置时间轴格式（修复时间轴问题）
            if all_windows:
                # 只有在有数据时才设置时间轴
                ax.xaxis.set_major_formatter(mdates.DateFormatter('%H:%M'))
                ax.xaxis.set_major_locator(mdates.MinuteLocator(interval=2))
                ax.set_xlim(left=min(mdates.date2num(w['start']) for w in all_windows),
                           right=max(mdates.date2num(w['stop']) for w in all_windows))
            else:
                # 没有数据时使用默认时间轴
                ax.xaxis.set_major_formatter(mdates.DateFormatter('%H:%M'))
                ax.xaxis.set_major_locator(mdates.HourLocator(interval=1))
            
            # 旋转时间标签
            plt.setp(ax.xaxis.get_majorticklabels(), rotation=45)
            
            # 添加网格
            ax.grid(True, alpha=0.3)
            
            # 调整布局
            plt.tight_layout()
            
            # 保存图形
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"constellation_visibility_{missile_id}_{timestamp}.png"
            
            if self.output_manager:
                output_path = self.output_manager.save_constellation_chart(filename)
            else:
                output_path = filename
            
            fig.savefig(output_path, dpi=300, bbox_inches='tight')
            plt.close(fig)
            
            # 检查文件
            if os.path.exists(output_path):
                file_size = os.path.getsize(output_path)
                logger.info(f"✅ 甘特图生成成功: {output_path}")
                logger.info(f"   文件大小: {file_size:,} 字节")
                logger.info(f"   可见窗口数: {len(all_windows)}")
                logger.info(f"   有可见性的卫星: {len([s for s in satellites if any(w['satellite'] == s for w in all_windows)])}")
                
                return output_path
            else:
                logger.error("❌ 甘特图文件未生成")
                return None
                
        except Exception as e:
            logger.error(f"❌ 甘特图生成失败: {e}")
            return None
    
    def _parse_time(self, time_str: str) -> Optional[datetime]:
        """解析时间字符串"""
        try:
            # 尝试不同的时间格式
            formats = [
                '%d %b %Y %H:%M:%S.%f',      # STK格式: "14 May 2025 04:12:56.535"
                '%d %b %Y %H:%M:%S',         # STK格式无微秒: "14 May 2025 04:12:56"
                '%Y-%m-%dT%H:%M:%S.%fZ',
                '%Y-%m-%dT%H:%M:%SZ',
                '%Y-%m-%dT%H:%M:%S',
                '%Y-%m-%d %H:%M:%S'
            ]
            
            for fmt in formats:
                try:
                    return datetime.strptime(time_str, fmt)
                except ValueError:
                    continue
            
            # 如果所有格式都失败，尝试ISO格式
            try:
                return datetime.fromisoformat(time_str.replace('Z', '+00:00'))
            except:
                pass
            
            logger.warning(f"⚠️  时间解析失败: {time_str}")
            return None
            
        except Exception as e:
            logger.error(f"❌ 时间解析异常: {e}")
            return None
