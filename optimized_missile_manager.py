"""
优化的STK导弹目标管理器
基于STK官方文档: https://help.agi.com/stkdevkit/12.7.0/index.htm#stkObjects/ObjModPythonCodeSamples.htm
严格遵循STK官方API最佳实践
"""

import logging
import win32com.client
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
import math

logger = logging.getLogger(__name__)

class OptimizedMissileManager:
    """
    优化的STK导弹目标管理器
    基于STK官方文档的最佳实践实现
    """
    
    def __init__(self, stk_manager):
        """初始化导弹管理器"""
        self.stk_manager = stk_manager
        self.missile_objects = {}
        self.earth_radius = 6371000  # 地球半径（米）
        
        # STK对象类型常量（基于官方文档）
        self.STK_MISSILE = 13  # eMissile
        self.STK_BALLISTIC_PROPAGATOR = 10  # ePropagatorBallistic
        self.STK_LAUNCH_CONTROL_FIXED_APOGEE = 0  # eLaunchControlFixedApogeeAlt
        
    def create_ballistic_missile(self, missile_config: Dict[str, Any]) -> Optional[Any]:
        """
        创建弹道导弹对象
        严格按照STK官方文档示例实现
        
        Args:
            missile_config: 导弹配置参数
            
        Returns:
            STK导弹对象或None
        """
        try:
            missile_id = missile_config["missile_id"]
            launch_pos = missile_config["launch_position"]
            target_pos = missile_config["target_position"]
            
            logger.info(f"🚀 创建弹道导弹: {missile_id}")
            
            # 1. 检查并删除现有对象
            self._cleanup_existing_missile(missile_id)
            
            # 2. 创建导弹对象 - 基于官方文档
            missile = self.stk_manager.scenario.Children.New(self.STK_MISSILE, missile_id)
            logger.info(f"✅ 导弹对象创建成功: {missile_id}")
            
            # 3. 设置轨迹类型为弹道 - 基于官方文档
            missile.SetTrajectoryType(self.STK_BALLISTIC_PROPAGATOR)
            
            # 4. 获取轨迹对象
            trajectory = missile.Trajectory
            
            # 5. 设置发射位置 - 基于官方文档示例
            trajectory.Launch.Lat = launch_pos["lat"]
            trajectory.Launch.Lon = launch_pos["lon"] 
            trajectory.Launch.Alt = launch_pos["alt"]
            
            # 6. 设置撞击位置 - 基于官方文档示例
            trajectory.ImpactLocation.Impact.Lat = target_pos["lat"]
            trajectory.ImpactLocation.Impact.Lon = target_pos["lon"]
            trajectory.ImpactLocation.Impact.Alt = target_pos["alt"]
            
            # 7. 设置发射控制类型 - 基于官方文档
            trajectory.ImpactLocation.SetLaunchControlType(self.STK_LAUNCH_CONTROL_FIXED_APOGEE)
            
            # 8. 计算并设置远地点高度
            range_km = self._calculate_great_circle_distance(launch_pos, target_pos) / 1000.0
            apogee_alt_km = self._calculate_optimal_apogee_altitude(range_km)
            trajectory.ImpactLocation.LaunchControl.ApogeeAlt = apogee_alt_km
            
            # 9. 设置发射时间 - 关键：确保在场景时间范围内
            self._set_launch_time(trajectory, missile_config.get("launch_offset_minutes", 1))
            
            # 10. 传播轨迹 - 基于官方文档
            trajectory.Propagate()
            
            # 11. 存储导弹信息
            missile_info = {
                "stk_object": missile,
                "config": missile_config,
                "trajectory_params": {
                    "range_km": range_km,
                    "apogee_alt_km": apogee_alt_km,
                    "flight_time_sec": self._estimate_flight_time(range_km)
                },
                "created_time": datetime.now().isoformat()
            }
            self.missile_objects[missile_id] = missile_info
            
            logger.info(f"✅ 弹道导弹 {missile_id} 创建完成")
            logger.info(f"   射程: {range_km:.1f} km")
            logger.info(f"   远地点高度: {apogee_alt_km:.1f} km")
            logger.info(f"   估算飞行时间: {missile_info['trajectory_params']['flight_time_sec']:.1f} 秒")
            
            return missile
            
        except Exception as e:
            logger.error(f"❌ 创建弹道导弹失败: {e}")
            return None
    
    def _cleanup_existing_missile(self, missile_id: str):
        """清理现有的导弹对象"""
        try:
            existing_missile = self.stk_manager.scenario.Children.Item(missile_id)
            if existing_missile:
                existing_missile.Unload()
                logger.info(f"🗑️  已删除现有导弹: {missile_id}")
        except:
            # 对象不存在，无需清理
            pass
    
    def _set_launch_time(self, trajectory, offset_minutes: int = 1):
        """
        设置发射时间
        确保导弹在场景时间范围内发射
        """
        try:
            # 获取场景开始时间
            scenario_start = self.stk_manager.scenario.StartTime
            
            # 解析场景开始时间并添加偏移
            try:
                # STK时间格式: "DD MMM YYYY HH:MM:SS.sss"
                start_dt = datetime.strptime(scenario_start, "%d %b %Y %H:%M:%S.%f")
            except:
                try:
                    start_dt = datetime.strptime(scenario_start, "%d %b %Y %H:%M:%S")
                except:
                    logger.warning("无法解析场景开始时间，使用当前时间")
                    start_dt = datetime.now()
            
            # 设置发射时间为场景开始后指定分钟
            launch_time = start_dt + timedelta(minutes=offset_minutes)
            launch_time_str = launch_time.strftime("%d %b %Y %H:%M:%S.000")
            
            # 设置发射时间 - 基于STK官方文档的多种方法
            try:
                # 方法1: 直接设置Launch.Time
                trajectory.Launch.Time = launch_time_str
                logger.info(f"⏰ 发射时间设置成功 (方法1): {launch_time_str}")
            except Exception as e1:
                logger.debug(f"方法1失败: {e1}")
                try:
                    # 方法2: 使用SetLaunchTime方法
                    trajectory.SetLaunchTime(launch_time_str)
                    logger.info(f"⏰ 发射时间设置成功 (方法2): {launch_time_str}")
                except Exception as e2:
                    logger.debug(f"方法2失败: {e2}")
                    try:
                        # 方法3: 设置轨迹开始时间
                        trajectory.EpochTime = launch_time_str
                        logger.info(f"⏰ 轨迹时间设置成功 (方法3): {launch_time_str}")
                    except Exception as e3:
                        logger.warning(f"所有时间设置方法都失败: 方法1({e1}), 方法2({e2}), 方法3({e3})")
                        # 使用场景开始时间作为默认值
                        logger.info(f"⏰ 使用场景开始时间作为默认发射时间: {scenario_start}")
            
        except Exception as e:
            logger.warning(f"设置发射时间失败: {e}")
    
    def _calculate_great_circle_distance(self, pos1: Dict, pos2: Dict) -> float:
        """计算大圆距离（米）"""
        lat1, lon1 = math.radians(pos1["lat"]), math.radians(pos1["lon"])
        lat2, lon2 = math.radians(pos2["lat"]), math.radians(pos2["lon"])
        
        dlat = lat2 - lat1
        dlon = lon2 - lon1
        
        a = math.sin(dlat/2)**2 + math.cos(lat1) * math.cos(lat2) * math.sin(dlon/2)**2
        c = 2 * math.atan2(math.sqrt(a), math.sqrt(1-a))
        
        return self.earth_radius * c
    
    def _calculate_optimal_apogee_altitude(self, range_km: float) -> float:
        """计算最优远地点高度（km）"""
        # 基于弹道导弹的经验公式
        if range_km < 1000:
            return 150 + range_km * 0.2  # 短程导弹
        elif range_km < 5000:
            return 300 + range_km * 0.15  # 中程导弹
        else:
            return 500 + range_km * 0.1   # 洲际导弹
    
    def _estimate_flight_time(self, range_km: float) -> float:
        """估算飞行时间（秒）"""
        # 基于弹道导弹的经验公式
        return 60 * math.sqrt(range_km / 1000.0) * 8.5
    
    def get_missile_info(self, missile_id: str) -> Optional[Dict]:
        """获取导弹信息"""
        return self.missile_objects.get(missile_id)
    
    def list_missiles(self) -> List[str]:
        """列出所有导弹ID"""
        return list(self.missile_objects.keys())
    
    def cleanup_all_missiles(self):
        """清理所有导弹对象"""
        for missile_id in list(self.missile_objects.keys()):
            try:
                self._cleanup_existing_missile(missile_id)
                del self.missile_objects[missile_id]
            except Exception as e:
                logger.warning(f"清理导弹 {missile_id} 失败: {e}")
        
        logger.info("🗑️  所有导弹对象已清理")
