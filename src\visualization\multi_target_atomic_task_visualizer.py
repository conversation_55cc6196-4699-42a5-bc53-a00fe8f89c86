"""
多目标原子任务对齐可视化器
将多个目标的原子任务通过对齐的时间段在一张图中进行可视化显示
"""

import matplotlib.pyplot as plt
import matplotlib.patches as patches
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import logging

logger = logging.getLogger(__name__)

class MultiTargetAtomicTaskVisualizer:
    """多目标原子任务对齐可视化器"""
    
    def __init__(self):
        """初始化可视化器"""
        self.fig = None
        self.ax = None
        
        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False
        
        # 颜色配置
        self.colors = {
            'task_fill': '#808080',      # 灰色填充
            'task_border': '#000000',    # 黑色边框
            'time_marker': '#FF0000',    # 红色时间标记线
            'text': '#000000',           # 文本颜色
            'grid': '#CCCCCC',           # 网格颜色
            'atomic_task': '#FFFFFF'     # 原子任务圆圈填充
        }
        
        # 目标颜色映射
        self.target_colors = [
            '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7',
            '#DDA0DD', '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9'
        ]
    
    def create_multi_target_aligned_chart(self, targets_task_info: Dict[str, Dict[str, Any]],
                                        output_manager=None, save_path: Optional[str] = None) -> str:
        """
        创建多目标原子任务对齐甘特图
        
        Args:
            targets_task_info: 多个目标的任务信息 {target_id: original_task_info}
            output_manager: 输出管理器
            save_path: 保存路径（可选）
            
        Returns:
            str: 保存的文件路径
        """
        try:
            logger.info(f"🎨 创建多目标原子任务对齐甘特图，目标数量: {len(targets_task_info)}")
            
            if not targets_task_info:
                logger.warning("没有目标任务信息，无法生成图表")
                return ""
            
            # 创建图形
            self.fig, self.ax = plt.subplots(figsize=(16, 10))
            
            # 计算全局时间范围
            global_start_time, global_end_time = self._calculate_global_time_range(targets_task_info)
            
            if not global_start_time or not global_end_time:
                logger.warning("无法计算全局时间范围")
                return ""
            
            # 绘制多目标对齐甘特图
            self._draw_multi_target_aligned_chart(targets_task_info, global_start_time, global_end_time)
            
            # 保存图片 - 使用输出管理器
            if save_path is None:
                if output_manager:
                    save_path = output_manager.save_gantt_chart("multi_target_aligned_chart.png")
                else:
                    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                    save_path = f"multi_target_atomic_tasks_{timestamp}.png"
            
            self.fig.savefig(save_path, dpi=300, bbox_inches='tight')
            logger.info(f"多目标原子任务甘特图已保存: {save_path}")
            
            return save_path
            
        except Exception as e:
            logger.error(f"❌ 创建多目标原子任务对齐甘特图失败: {e}")
            return ""
    
    def _calculate_global_time_range(self, targets_task_info: Dict[str, Dict[str, Any]]) -> tuple:
        """计算全局时间范围"""
        try:
            all_start_times = []
            all_end_times = []

            for target_id, task_info in targets_task_info.items():
                # 从tracking_task中获取时间信息
                tracking_task = task_info.get("tracking_task", {})
                start_time = tracking_task.get("start_time")
                end_time = tracking_task.get("end_time")

                # 如果tracking_task中没有，尝试从根级别获取
                if not start_time:
                    start_time = task_info.get("start_time")
                if not end_time:
                    end_time = task_info.get("end_time")

                # 处理时间格式转换
                if start_time:
                    if isinstance(start_time, str):
                        try:
                            start_time = datetime.fromisoformat(start_time.replace('Z', '+00:00'))
                        except:
                            logger.debug(f"无法解析开始时间: {start_time}")
                            continue
                    all_start_times.append(start_time)

                if end_time:
                    if isinstance(end_time, str):
                        try:
                            end_time = datetime.fromisoformat(end_time.replace('Z', '+00:00'))
                        except:
                            logger.debug(f"无法解析结束时间: {end_time}")
                            continue
                    all_end_times.append(end_time)

            if not all_start_times or not all_end_times:
                logger.warning(f"无法获取有效的时间信息，任务数据: {list(targets_task_info.keys())}")
                # 打印调试信息
                for target_id, task_info in targets_task_info.items():
                    logger.debug(f"任务 {target_id} 的时间信息: {task_info.get('tracking_task', {})}")
                return None, None

            global_start = min(all_start_times)
            global_end = max(all_end_times)

            logger.info(f"   全局时间范围: {global_start} - {global_end}")
            return global_start, global_end

        except Exception as e:
            logger.error(f"计算全局时间范围失败: {e}")
            return None, None
    
    def _draw_multi_target_aligned_chart(self, targets_task_info: Dict[str, Dict[str, Any]], 
                                       global_start_time: datetime, global_end_time: datetime):
        """绘制多目标对齐甘特图"""
        try:
            target_ids = list(targets_task_info.keys())
            num_targets = len(target_ids)
            
            # 计算全局时间范围（秒）
            global_duration = (global_end_time - global_start_time).total_seconds()
            
            # 设置Y轴位置
            y_positions = np.arange(num_targets)
            bar_height = 0.6
            
            # 为每个目标绘制任务条
            for i, target_id in enumerate(target_ids):
                task_info = targets_task_info[target_id]
                color = self.target_colors[i % len(self.target_colors)]

                # 从tracking_task中获取时间信息
                tracking_task = task_info.get("tracking_task", {})
                task_start = tracking_task.get("start_time")
                task_end = tracking_task.get("end_time")

                # 如果tracking_task中没有，尝试从根级别获取
                if not task_start:
                    task_start = task_info.get("start_time")
                if not task_end:
                    task_end = task_info.get("end_time")

                # 处理时间格式转换
                if isinstance(task_start, str):
                    try:
                        task_start = datetime.fromisoformat(task_start.replace('Z', '+00:00'))
                    except:
                        logger.debug(f"无法解析任务开始时间: {task_start}")
                        continue

                if isinstance(task_end, str):
                    try:
                        task_end = datetime.fromisoformat(task_end.replace('Z', '+00:00'))
                    except:
                        logger.debug(f"无法解析任务结束时间: {task_end}")
                        continue

                if not task_start or not task_end:
                    logger.debug(f"任务 {target_id} 缺少时间信息")
                    continue
                
                # 计算相对于全局开始时间的偏移（秒）
                start_offset = (task_start - global_start_time).total_seconds()
                task_duration = (task_end - task_start).total_seconds()
                
                # 绘制任务条
                task_rect = patches.Rectangle(
                    (start_offset, i - bar_height/2), task_duration, bar_height,
                    linewidth=2, edgecolor=self.colors['task_border'],
                    facecolor=color, alpha=0.7
                )
                self.ax.add_patch(task_rect)
                
                # 添加目标标签
                self.ax.text(-global_duration * 0.02, i, target_id, 
                           fontsize=10, ha='right', va='center', fontweight='bold')
                
                # 绘制原子任务标记
                self._draw_atomic_tasks(task_info, start_offset, i, bar_height)
                
                # 添加时间标记
                self._add_task_time_markers(start_offset, start_offset + task_duration, 
                                          task_start, task_end, i, bar_height)
            
            # 设置坐标轴
            self._setup_aligned_chart_axes(target_ids, global_duration, global_start_time)
            
        except Exception as e:
            logger.error(f"绘制多目标对齐甘特图失败: {e}")
    
    def _draw_atomic_tasks(self, task_info: Dict[str, Any], start_offset: float, y_pos: int, bar_height: float):
        """绘制原子任务标记"""
        try:
            # 从tracking_task中获取原子任务信息
            tracking_task = task_info.get("tracking_task", {})
            atomic_tasks = tracking_task.get("atomic_tasks", [])

            # 如果tracking_task中没有，尝试从根级别获取
            if not atomic_tasks:
                atomic_tasks = task_info.get("atomic_tasks", [])

            # 计算任务持续时间
            task_start = tracking_task.get("start_time") or task_info.get("start_time")
            task_end = tracking_task.get("end_time") or task_info.get("end_time")

            if isinstance(task_start, str):
                try:
                    task_start = datetime.fromisoformat(task_start.replace('Z', '+00:00'))
                except:
                    task_start = None

            if isinstance(task_end, str):
                try:
                    task_end = datetime.fromisoformat(task_end.replace('Z', '+00:00'))
                except:
                    task_end = None

            if task_start and task_end:
                task_duration = (task_end - task_start).total_seconds()
            else:
                task_duration = tracking_task.get("duration", 0)

            if not atomic_tasks or task_duration <= 0:
                logger.debug(f"任务缺少原子任务信息或持续时间: 原子任务数={len(atomic_tasks)}, 持续时间={task_duration}")
                return

            # 计算每个原子任务的位置
            for i, atomic_task in enumerate(atomic_tasks):
                # 原子任务在任务条内的相对位置
                relative_position = (i + 0.5) / len(atomic_tasks)
                atomic_x = start_offset + relative_position * task_duration

                # 绘制原子任务圆圈
                circle = patches.Circle(
                    (atomic_x, y_pos), bar_height/6,
                    linewidth=1, edgecolor=self.colors['task_border'],
                    facecolor=self.colors['atomic_task'], alpha=0.9
                )
                self.ax.add_patch(circle)

                # 添加原子任务序号
                self.ax.text(atomic_x, y_pos, str(i+1),
                           fontsize=8, ha='center', va='center', fontweight='bold')

            logger.debug(f"绘制了 {len(atomic_tasks)} 个原子任务标记")

        except Exception as e:
            logger.error(f"绘制原子任务标记失败: {e}")
    
    def _add_task_time_markers(self, start_offset: float, end_offset: float, 
                             start_time: datetime, end_time: datetime, y_pos: int, bar_height: float):
        """添加任务时间标记"""
        try:
            # 绘制开始时间标记
            self.ax.axvline(x=start_offset, ymin=(y_pos - bar_height/2 + len(self.target_colors)/2) / len(self.target_colors), 
                          ymax=(y_pos + bar_height/2 + len(self.target_colors)/2) / len(self.target_colors), 
                          color=self.colors['time_marker'], linewidth=1, linestyle='--', alpha=0.7)
            
            # 绘制结束时间标记
            self.ax.axvline(x=end_offset, ymin=(y_pos - bar_height/2 + len(self.target_colors)/2) / len(self.target_colors), 
                          ymax=(y_pos + bar_height/2 + len(self.target_colors)/2) / len(self.target_colors), 
                          color=self.colors['time_marker'], linewidth=1, linestyle='--', alpha=0.7)
            
        except Exception as e:
            logger.error(f"添加任务时间标记失败: {e}")
    
    def _setup_aligned_chart_axes(self, target_ids: List[str], global_duration: float, global_start_time: datetime):
        """设置对齐图表坐标轴"""
        try:
            # 设置X轴
            self.ax.set_xlim(-global_duration * 0.1, global_duration * 1.1)
            self.ax.set_xlabel("时间 (秒)", fontsize=12, fontweight='bold')
            
            # 设置时间刻度
            time_ticks = np.linspace(0, global_duration, 6)
            self.ax.set_xticks(time_ticks)
            self.ax.set_xticklabels([f"{int(t)}s" for t in time_ticks], fontsize=10)
            
            # 设置Y轴
            self.ax.set_ylim(-0.5, len(target_ids) - 0.5)
            self.ax.set_ylabel("目标导弹", fontsize=12, fontweight='bold')
            self.ax.set_yticks(range(len(target_ids)))
            self.ax.set_yticklabels(target_ids, fontsize=10)
            
            # 设置标题
            title = f'多目标原子任务对齐甘特图\n起始时间: {global_start_time.strftime("%Y-%m-%d %H:%M:%S")}'
            self.ax.set_title(title, fontsize=14, fontweight='bold', pad=20)
            
            # 添加网格
            self.ax.grid(True, alpha=0.3, color=self.colors['grid'])
            
            # 调整布局
            plt.tight_layout()
            
        except Exception as e:
            logger.error(f"设置对齐图表坐标轴失败: {e}")
    
    def close(self):
        """关闭图形"""
        if self.fig:
            plt.close(self.fig)
            self.fig = None
            self.ax = None
