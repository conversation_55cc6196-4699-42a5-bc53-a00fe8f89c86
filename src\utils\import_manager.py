#!/usr/bin/env python3
"""
统一导入管理器 - ICBM预警系统
基于成功经验重构导入模块管理，确保导入逻辑清晰明了
"""

import logging
import sys
import importlib
from typing import Dict, Any, Optional, List, Tuple
import traceback

logger = logging.getLogger(__name__)


class ImportManager:
    """
    统一导入管理器
    
    功能：
    1. 管理所有模块的导入状态
    2. 提供导入失败的回退机制
    3. 记录导入成功经验
    4. 统一错误处理和日志记录
    """
    
    def __init__(self):
        """初始化导入管理器"""
        self.import_status = {}
        self.import_errors = {}
        self.successful_imports = {}
        self.failed_imports = {}
        
        logger.info("🔧 统一导入管理器初始化完成")
    
    def import_adk_framework(self) -> Tuple[bool, Dict[str, Any]]:
        """
        导入ADK框架 - 基于成功经验
        
        Returns:
            (success, components) 导入是否成功和组件字典
        """
        try:
            logger.info("🚀 开始导入ADK框架...")
            
            # 基于成功经验的导入路径
            adk_components = {}
            
            # 1. 导入核心ADK组件
            try:
                from google.adk import Agent as LlmAgent
                from google.adk.tools import FunctionTool
                from google.adk.sessions import Session
                from google.adk.memory import InMemoryMemoryService
                
                adk_components.update({
                    'LlmAgent': LlmAgent,
                    'FunctionTool': FunctionTool,
                    'Session': Session,
                    'InMemoryMemoryService': InMemoryMemoryService
                })
                
                logger.info("✅ ADK核心组件导入成功")
                logger.info(f"   - Agent类: {LlmAgent}")
                logger.info(f"   - FunctionTool: {FunctionTool}")
                logger.info(f"   - Session: {Session}")
                logger.info(f"   - InMemoryMemoryService: {InMemoryMemoryService}")
                
            except ImportError as e:
                logger.error(f"❌ ADK核心组件导入失败: {e}")
                self.import_errors['adk_core'] = str(e)
                return False, {}
            
            # 2. 记录成功导入
            self.successful_imports['adk_framework'] = {
                'components': list(adk_components.keys()),
                'import_path': 'google.adk',
                'status': 'success'
            }
            
            logger.info("🎉 ADK框架导入完全成功")
            return True, adk_components
            
        except Exception as e:
            logger.error(f"❌ ADK框架导入失败: {e}")
            self.failed_imports['adk_framework'] = str(e)
            return False, {}
    
    def import_stk_interface(self) -> Tuple[bool, Dict[str, Any]]:
        """
        导入STK接口组件
        
        Returns:
            (success, components) 导入是否成功和组件字典
        """
        try:
            logger.info("📡 开始导入STK接口组件...")
            
            stk_components = {}
            
            # 1. 导入win32com
            try:
                import win32com.client
                stk_components['win32com'] = win32com.client
                logger.info("✅ win32com.client导入成功")
            except ImportError as e:
                logger.error(f"❌ win32com.client导入失败: {e}")
                self.import_errors['win32com'] = str(e)
                return False, {}
            
            # 2. 导入STK管理器
            try:
                from src.stk_interface.stk_manager import STKManager
                from src.stk_interface.missile_manager import MissileManager
                from src.stk_interface.visibility_calculator import VisibilityCalculator
                
                stk_components.update({
                    'STKManager': STKManager,
                    'MissileManager': MissileManager,
                    'VisibilityCalculator': VisibilityCalculator
                })
                
                logger.info("✅ STK接口组件导入成功")
                
            except ImportError as e:
                logger.error(f"❌ STK接口组件导入失败: {e}")
                self.import_errors['stk_interface'] = str(e)
                return False, {}
            
            # 3. 记录成功导入
            self.successful_imports['stk_interface'] = {
                'components': list(stk_components.keys()),
                'status': 'success'
            }
            
            return True, stk_components
            
        except Exception as e:
            logger.error(f"❌ STK接口导入失败: {e}")
            self.failed_imports['stk_interface'] = str(e)
            return False, {}
    
    def import_adk_agents(self) -> Tuple[bool, Dict[str, Any]]:
        """
        导入ADK智能体组件 - 基于成功经验
        
        Returns:
            (success, components) 导入是否成功和组件字典
        """
        try:
            logger.info("🤖 开始导入ADK智能体组件...")
            
            agent_components = {}
            
            # 1. 导入真正的ADK智能体 (优先级最高)
            try:
                from src.adk_framework.real_adk_agent import RealADKSatelliteAgent
                agent_components['RealADKSatelliteAgent'] = RealADKSatelliteAgent
                logger.info("✅ 真正的ADK智能体导入成功")
            except ImportError as e:
                logger.warning(f"⚠️ 真正的ADK智能体导入失败: {e}")
                self.import_errors['real_adk_agent'] = str(e)
            
            # 2. 导入增强版ADK智能体
            try:
                from src.adk_framework.enhanced_adk_agent import EnhancedADKSatelliteAgent
                agent_components['EnhancedADKSatelliteAgent'] = EnhancedADKSatelliteAgent
                logger.info("✅ 增强版ADK智能体导入成功")
            except ImportError as e:
                logger.warning(f"⚠️ 增强版ADK智能体导入失败: {e}")
                self.import_errors['enhanced_adk_agent'] = str(e)
            
            # 3. 导入卫星智能体映射器
            try:
                from src.adk_framework.satellite_agent_mapper import SatelliteAgentMapper
                from src.adk_framework.distributed_agent_manager import DistributedAgentManager
                from src.adk_framework.meta_task_coordinator import MetaTaskCoordinator
                
                agent_components.update({
                    'SatelliteAgentMapper': SatelliteAgentMapper,
                    'DistributedAgentManager': DistributedAgentManager,
                    'MetaTaskCoordinator': MetaTaskCoordinator
                })
                
                logger.info("✅ ADK框架组件导入成功")
                
            except ImportError as e:
                logger.warning(f"⚠️ ADK框架组件导入失败: {e}")
                self.import_errors['adk_framework_components'] = str(e)
            
            # 4. 检查是否有可用的智能体
            if not agent_components:
                logger.error("❌ 没有可用的ADK智能体组件")
                return False, {}
            
            # 5. 记录成功导入
            self.successful_imports['adk_agents'] = {
                'components': list(agent_components.keys()),
                'status': 'success'
            }
            
            logger.info(f"🎉 ADK智能体组件导入成功: {len(agent_components)}个组件")
            return True, agent_components
            
        except Exception as e:
            logger.error(f"❌ ADK智能体组件导入失败: {e}")
            self.failed_imports['adk_agents'] = str(e)
            return False, {}
    
    def import_visualization_components(self) -> Tuple[bool, Dict[str, Any]]:
        """
        导入可视化组件
        
        Returns:
            (success, components) 导入是否成功和组件字典
        """
        try:
            logger.info("🎨 开始导入可视化组件...")
            
            viz_components = {}
            
            # 1. 导入matplotlib
            try:
                import matplotlib.pyplot as plt
                import matplotlib.dates as mdates
                from matplotlib.patches import Rectangle
                import matplotlib
                
                # 设置中文字体支持
                matplotlib.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
                matplotlib.rcParams['axes.unicode_minus'] = False
                
                viz_components.update({
                    'plt': plt,
                    'mdates': mdates,
                    'Rectangle': Rectangle,
                    'matplotlib': matplotlib
                })
                
                logger.info("✅ matplotlib组件导入成功")
                
            except ImportError as e:
                logger.error(f"❌ matplotlib导入失败: {e}")
                self.import_errors['matplotlib'] = str(e)
                return False, {}
            
            # 2. 导入甘特图生成器
            try:
                from src.visualization.gantt_generator import GanttGenerator
                viz_components['GanttGenerator'] = GanttGenerator
                logger.info("✅ 甘特图生成器导入成功")
            except ImportError as e:
                logger.warning(f"⚠️ 甘特图生成器导入失败: {e}")
                self.import_errors['gantt_generator'] = str(e)
            
            # 3. 导入其他可视化组件
            try:
                from src.visualization.multi_target_atomic_task_visualizer import MultiTargetAtomicTaskVisualizer
                from src.visualization.multi_target_meta_task_visualizer import MultiTargetMetaTaskVisualizer
                
                viz_components.update({
                    'MultiTargetAtomicTaskVisualizer': MultiTargetAtomicTaskVisualizer,
                    'MultiTargetMetaTaskVisualizer': MultiTargetMetaTaskVisualizer
                })
                
                logger.info("✅ 多目标可视化组件导入成功")
                
            except ImportError as e:
                logger.warning(f"⚠️ 多目标可视化组件导入失败: {e}")
                self.import_errors['multi_target_visualizers'] = str(e)
            
            # 4. 记录成功导入
            self.successful_imports['visualization'] = {
                'components': list(viz_components.keys()),
                'status': 'success'
            }
            
            logger.info(f"🎉 可视化组件导入成功: {len(viz_components)}个组件")
            return True, viz_components
            
        except Exception as e:
            logger.error(f"❌ 可视化组件导入失败: {e}")
            self.failed_imports['visualization'] = str(e)
            return False, {}
    
    def import_utility_components(self) -> Tuple[bool, Dict[str, Any]]:
        """
        导入工具组件
        
        Returns:
            (success, components) 导入是否成功和组件字典
        """
        try:
            logger.info("🔧 开始导入工具组件...")
            
            util_components = {}
            
            # 1. 导入配置管理器
            try:
                from src.utils.config_manager import ConfigManager, get_config_manager
                util_components.update({
                    'ConfigManager': ConfigManager,
                    'get_config_manager': get_config_manager
                })
                logger.info("✅ 配置管理器导入成功")
            except ImportError as e:
                logger.error(f"❌ 配置管理器导入失败: {e}")
                self.import_errors['config_manager'] = str(e)
                return False, {}
            
            # 2. 导入时间管理器
            try:
                from src.utils.time_manager import UnifiedTimeManager, get_time_manager
                util_components.update({
                    'UnifiedTimeManager': UnifiedTimeManager,
                    'get_time_manager': get_time_manager
                })
                logger.info("✅ 时间管理器导入成功")
            except ImportError as e:
                logger.error(f"❌ 时间管理器导入失败: {e}")
                self.import_errors['time_manager'] = str(e)
                return False, {}
            
            # 3. 导入输出管理器
            try:
                from src.utils.output_manager import OutputManager
                util_components['OutputManager'] = OutputManager
                logger.info("✅ 输出管理器导入成功")
            except ImportError as e:
                logger.error(f"❌ 输出管理器导入失败: {e}")
                self.import_errors['output_manager'] = str(e)
                return False, {}
            
            # 4. 记录成功导入
            self.successful_imports['utilities'] = {
                'components': list(util_components.keys()),
                'status': 'success'
            }
            
            logger.info(f"🎉 工具组件导入成功: {len(util_components)}个组件")
            return True, util_components
            
        except Exception as e:
            logger.error(f"❌ 工具组件导入失败: {e}")
            self.failed_imports['utilities'] = str(e)
            return False, {}
    
    def get_import_summary(self) -> Dict[str, Any]:
        """
        获取导入状态摘要
        
        Returns:
            导入状态摘要
        """
        total_successful = len(self.successful_imports)
        total_failed = len(self.failed_imports)
        total_errors = len(self.import_errors)
        
        return {
            'summary': {
                'total_successful': total_successful,
                'total_failed': total_failed,
                'total_errors': total_errors,
                'success_rate': total_successful / (total_successful + total_failed) if (total_successful + total_failed) > 0 else 0
            },
            'successful_imports': self.successful_imports,
            'failed_imports': self.failed_imports,
            'import_errors': self.import_errors
        }


# 全局导入管理器实例
_global_import_manager = None


def get_import_manager() -> ImportManager:
    """
    获取全局导入管理器实例
    
    Returns:
        ImportManager: 导入管理器实例
    """
    global _global_import_manager
    
    if _global_import_manager is None:
        _global_import_manager = ImportManager()
    
    return _global_import_manager
