"""
导弹管理器 - 清理版本
负责管理STK场景中的导弹对象，包括创建、配置和轨迹计算
"""

import logging
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta
import math

logger = logging.getLogger(__name__)

class MissileManager:
    """导弹管理器 - 重新设计的导弹对象状态管理"""
    
    def __init__(self, stk_manager, config: Dict[str, Any], output_manager):
        """初始化导弹管理器"""
        self.stk_manager = stk_manager
        self.config = config
        self.output_manager = output_manager
        # 从配置管理器获取时间管理器
        from src.utils.time_manager import get_time_manager
        from src.utils.config_manager import get_config_manager
        config_manager = get_config_manager()
        self.time_manager = get_time_manager(config_manager)
        self.missile_targets = {}
        
    def add_missile_target(self, missile_id: str, launch_position: Dict[str, float], 
                          target_position: Dict[str, float], launch_sequence: int = 1):
        """添加导弹目标配置"""
        self.missile_targets[missile_id] = {
            "launch_position": launch_position,
            "target_position": target_position,
            "launch_sequence": launch_sequence
        }
        logger.info(f"✅ 添加导弹目标配置: {missile_id}")
        
    def create_missile(self, missile_id: str, launch_time: datetime) -> bool:
        """创建导弹对象"""
        try:
            logger.info(f"🚀 创建导弹对象: {missile_id}")
            
            # 获取导弹配置
            missile_info = self.missile_targets.get(missile_id)
            if not missile_info:
                logger.error(f"❌ 未找到导弹配置: {missile_id}")
                return False
                
            # 准备轨迹参数
            trajectory_params = {
                "launch_position": missile_info["launch_position"],
                "target_position": missile_info["target_position"]
            }
            
            # 创建STK导弹对象
            success = self._create_stk_missile_object(missile_id, launch_time, trajectory_params)
            
            if success:
                logger.info(f"✅ 导弹对象创建成功: {missile_id}")
                return True
            else:
                logger.error(f"❌ 导弹对象创建失败: {missile_id}")
                return False
                
        except Exception as e:
            logger.error(f"❌ 创建导弹失败: {e}")
            return False
            
    def _create_stk_missile_object(self, missile_id: str, launch_time: datetime, 
                                  trajectory_params: Dict[str, Any]) -> bool:
        """创建STK导弹对象并配置轨迹"""
        try:
            logger.info(f"🎯 创建STK导弹对象: {missile_id}")
            
            # 1. 创建导弹对象
            try:
                missile = self.stk_manager.scenario.Children.New(13, missile_id)  # eMissile
                logger.info(f"✅ 导弹对象创建成功: {missile_id}")
            except Exception as create_error:
                logger.error(f"❌ 导弹对象创建失败: {create_error}")
                return False
                
            # 2. 设置轨迹类型为弹道
            try:
                missile.SetTrajectoryType(10)  # ePropagatorBallistic
                logger.info(f"✅ 轨迹类型设置为弹道: {missile.TrajectoryType}")
            except Exception as type_error:
                logger.error(f"❌ 轨迹类型设置失败: {type_error}")
                return False

            # 3. 设置导弹时间属性 - 基于STK官方文档的正确顺序
            # 重要：必须在设置轨迹类型后，配置轨迹参数前设置时间
            self._set_missile_time_period_correct(missile, launch_time)
                
            # 4. 配置轨迹参数
            try:
                trajectory = missile.Trajectory
                launch_pos = trajectory_params["launch_position"]
                target_pos = trajectory_params["target_position"]
                
                # 设置发射位置
                trajectory.Launch.Lat = launch_pos["lat"]
                trajectory.Launch.Lon = launch_pos["lon"]
                trajectory.Launch.Alt = launch_pos["alt"]
                logger.info(f"✅ 发射位置设置成功")
                
                # 设置撞击位置
                trajectory.ImpactLocation.Impact.Lat = target_pos["lat"]
                trajectory.ImpactLocation.Impact.Lon = target_pos["lon"]
                trajectory.ImpactLocation.Impact.Alt = target_pos["alt"]
                logger.info(f"✅ 撞击位置设置成功")
                
                # 设置发射控制类型和远地点高度
                range_m = self._calculate_great_circle_distance(launch_pos, target_pos)
                range_km = range_m / 1000.0
                apogee_alt_km = min(max(range_km * 0.3, 300), 1500)
                
                trajectory.ImpactLocation.SetLaunchControlType(0)
                trajectory.ImpactLocation.LaunchControl.ApogeeAlt = apogee_alt_km
                logger.info(f"✅ 发射控制设置成功: {apogee_alt_km:.1f}km")
                
                # 执行传播
                trajectory.Propagate()
                logger.info(f"✅ 轨迹传播成功")

                # 验证传播结果
                if self._verify_trajectory_propagation(missile):
                    logger.info(f"✅ 轨迹传播验证成功")
                else:
                    logger.warning(f"⚠️  轨迹传播验证失败，但继续执行")
                
            except Exception as traj_error:
                logger.warning(f"⚠️  轨迹参数设置失败: {traj_error}")
                
            return True
            
        except Exception as e:
            logger.error(f"❌ STK导弹对象创建失败: {e}")
            return False
            
    def get_missile_trajectory_info(self, missile_id: str) -> Optional[Dict[str, Any]]:
        """获取导弹轨迹信息 - 简化版本，直接从STK场景读取"""
        logger.info(f"🎯 获取导弹轨迹信息: {missile_id}")

        # 获取导弹对象
        missile = self.stk_manager.scenario.Children.Item(missile_id)
        logger.info(f"✅ 导弹对象获取成功: {missile_id}")

        # 直接从STK DataProvider获取轨迹数据
        return self._get_trajectory_from_stk_dataprovider(missile)
            
    def _calculate_great_circle_distance(self, pos1: Dict[str, float], pos2: Dict[str, float]) -> float:
        """计算两点间的大圆距离（米）"""
        try:
            # 转换为弧度
            lat1_rad = math.radians(pos1["lat"])
            lon1_rad = math.radians(pos1["lon"])
            lat2_rad = math.radians(pos2["lat"])
            lon2_rad = math.radians(pos2["lon"])
            
            # 使用Haversine公式
            dlat = lat2_rad - lat1_rad
            dlon = lon2_rad - lon1_rad
            
            a = math.sin(dlat/2)**2 + math.cos(lat1_rad) * math.cos(lat2_rad) * math.sin(dlon/2)**2
            c = 2 * math.asin(math.sqrt(a))
            
            # 地球半径（米）
            earth_radius = 6371000
            distance = earth_radius * c
            
            return distance
            
        except Exception as e:
            logger.error(f"距离计算失败: {e}")
            raise Exception(f"距离计算失败: {e}")

    def _set_missile_time_period_correct(self, missile, launch_time: datetime):
        """
        基于STK官方文档的正确导弹时间设置方法
        关键：导弹时间设置必须在轨迹类型设置后，轨迹参数配置前进行
        """
        try:
            # 获取场景时间范围
            scenario_start = self.stk_manager.scenario.StartTime
            scenario_stop = self.stk_manager.scenario.StopTime

            logger.info(f"📅 场景时间范围: {scenario_start} - {scenario_stop}")

            # 解析场景开始时间
            try:
                start_dt = datetime.strptime(scenario_start, "%d %b %Y %H:%M:%S.%f")
            except:
                try:
                    start_dt = datetime.strptime(scenario_start, "%d %b %Y %H:%M:%S")
                except:
                    logger.warning("无法解析场景开始时间，使用当前时间")
                    start_dt = datetime.now()

            # 确保发射时间在场景范围内
            if launch_time < start_dt:
                launch_time = start_dt + timedelta(minutes=1)
                logger.info(f"调整发射时间到场景开始后: {launch_time}")

            # 计算撞击时间（30分钟后）
            impact_time = launch_time + timedelta(minutes=30)

            # 转换为STK时间格式
            launch_time_str = launch_time.strftime("%d %b %Y %H:%M:%S.000")
            impact_time_str = impact_time.strftime("%d %b %Y %H:%M:%S.000")

            # 基于STK官方文档：导弹对象时间设置的正确方法
            success = False

            # 方法1: 使用轨迹对象的时间设置（推荐）
            try:
                trajectory = missile.Trajectory
                # 设置轨迹的时间范围
                trajectory.SetTimePeriod(launch_time_str, impact_time_str)
                logger.info(f"✅ 轨迹时间范围设置成功: {launch_time_str} - {impact_time_str}")
                success = True

            except Exception as e1:
                logger.debug(f"轨迹时间设置失败: {e1}")

                # 方法2: 使用Connect命令（STK通用方法）
                try:
                    missile_path = f"Missile/{missile.InstanceName}"
                    time_cmd = f"SetTimePeriod {missile_path} \"{launch_time_str}\" \"{impact_time_str}\""
                    self.stk_manager.root.ExecuteCommand(time_cmd)
                    logger.info(f"✅ Connect命令时间设置成功: {launch_time_str} - {impact_time_str}")
                    success = True

                except Exception as e2:
                    logger.debug(f"Connect命令时间设置失败: {e2}")

                    # 方法3: 使用导弹对象的SetTimePeriod方法
                    try:
                        missile.SetTimePeriod(launch_time_str, impact_time_str)
                        logger.info(f"✅ 导弹对象时间设置成功: {launch_time_str} - {impact_time_str}")
                        success = True

                    except Exception as e3:
                        logger.warning(f"所有时间设置方法都失败:")
                        logger.warning(f"  轨迹方法: {e1}")
                        logger.warning(f"  Connect命令: {e2}")
                        logger.warning(f"  导弹对象方法: {e3}")
                        logger.info(f"⏰ 将使用场景默认时间范围")

            # 如果时间设置成功，记录相关信息
            if success:
                logger.info(f"🎯 导弹时间设置完成:")
                logger.info(f"   发射时间: {launch_time_str}")
                logger.info(f"   撞击时间: {impact_time_str}")
                logger.info(f"   飞行时间: 30分钟")

        except Exception as e:
            logger.warning(f"导弹时间设置过程失败: {e}")
            logger.info(f"⏰ 将使用场景默认时间范围")

    def _convert_to_stk_time_format(self, dt: datetime) -> str:
        """将Python datetime转换为STK时间格式"""
        try:
            # 月份缩写映射
            month_abbr = {
                1: 'Jan', 2: 'Feb', 3: 'Mar', 4: 'Apr', 5: 'May', 6: 'Jun',
                7: 'Jul', 8: 'Aug', 9: 'Sep', 10: 'Oct', 11: 'Nov', 12: 'Dec'
            }

            # 格式化为STK时间字符串
            stk_time = f"{dt.day} {month_abbr[dt.month]} {dt.year} {dt.hour:02d}:{dt.minute:02d}:{dt.second:02d}.{dt.microsecond//1000:03d}"
            return stk_time

        except Exception as e:
            logger.error(f"时间格式转换失败: {e}")
            raise Exception(f"时间格式转换失败: {e}")

    def _get_trajectory_from_stk_dataprovider(self, missile) -> Dict[str, Any]:
        """从STK DataProvider获取轨迹数据 - 优先使用真实STK轨迹数据"""
        missile_id = missile.InstanceName
        logger.info(f"🎯 从STK DataProvider获取轨迹数据: {missile_id}")

        try:
            # 方法1: 从STK DataProvider获取真实轨迹数据 (优先推荐)
            logger.info(f"🎯 方法1: 尝试从STK DataProvider获取真实轨迹数据")
            real_trajectory = self._extract_real_trajectory_from_stk(missile)
            if real_trajectory:
                logger.info(f"✅ 方法1成功: 获取STK DataProvider真实轨迹数据")
                return real_trajectory
            else:
                logger.info(f"⚠️  方法1失败: DataProvider数据提取问题")

            # 方法2: 基于STK官方文档 - 从导弹对象直接获取轨迹 (备选)
            logger.info(f"🎯 方法2: 尝试从导弹轨迹对象直接获取数据")
            trajectory_data = self._get_trajectory_from_missile_object(missile, missile_id)
            if trajectory_data:
                data_quality = trajectory_data.get('stk_data_quality', {}).get('overall_quality', 'unknown')
                logger.info(f"✅ 方法2成功: 从导弹对象获取轨迹数据 (质量: {data_quality})")
                return trajectory_data
            else:
                logger.info(f"⚠️  方法2失败: 导弹对象数据不完整")

        except Exception as e:
            logger.warning(f"⚠️  STK轨迹获取失败: {e}")

        # 方法3: 回退到基于配置的轨迹生成 (保底)
        logger.info(f"🎯 方法3: 使用配置数据生成轨迹")
        return self._generate_trajectory_from_config(missile)

    def _get_trajectory_from_missile_object(self, missile, missile_id) -> Optional[Dict[str, Any]]:
        """
        基于STK官方文档: 直接从导弹轨迹对象获取数据
        参考: IAgVePropagatorBallistic Interface
        """
        try:
            logger.info(f"   🎯 基于STK官方文档从导弹对象获取轨迹: {missile_id}")

            # 基于STK官方文档: 获取导弹轨迹对象
            trajectory = missile.Trajectory
            logger.info(f"   ✅ 导弹轨迹对象获取成功")

            # 基于STK官方文档: 检查轨迹类型和状态
            try:
                # 获取轨迹的基本信息
                start_time = trajectory.StartTime if hasattr(trajectory, 'StartTime') else None
                stop_time = trajectory.StopTime if hasattr(trajectory, 'StopTime') else None
                step_size = trajectory.Step if hasattr(trajectory, 'Step') else 60.0

                logger.info(f"   📊 轨迹基本信息:")
                logger.info(f"      开始时间: {start_time}")
                logger.info(f"      结束时间: {stop_time}")
                logger.info(f"      步长: {step_size}秒")

                # 基于STK官方文档: 获取发射和撞击信息
                launch_info = self._get_launch_impact_info(trajectory)
                if launch_info and any(launch_info.values()):
                    logger.info(f"   ✅ 成功获取发射和撞击信息")
                    return self._build_trajectory_from_launch_impact(missile_id, launch_info, start_time, stop_time, step_size)
                else:
                    logger.info(f"   ⚠️  发射撞击信息不完整，尝试其他方法")

                # 尝试通过导弹对象的其他属性获取数据
                missile_data = self._get_missile_object_data(missile, missile_id)
                if missile_data:
                    logger.info(f"   ✅ 成功从导弹对象获取其他数据")
                    return missile_data

            except Exception as traj_info_error:
                logger.debug(f"   轨迹信息获取失败: {traj_info_error}")

            return None

        except Exception as e:
            logger.error(f"   ❌ 从导弹对象获取轨迹失败: {e}")
            return None

    def _get_missile_object_data(self, missile, missile_id) -> Optional[Dict[str, Any]]:
        """
        基于STK官方文档: 从导弹对象获取其他可用数据
        尝试多种方式获取导弹的真实数据
        """
        try:
            logger.info(f"   🔍 尝试从导弹对象获取其他数据: {missile_id}")

            missile_data = {}

            # 方法1: 尝试获取导弹的基本属性
            try:
                # 获取导弹的基本信息
                if hasattr(missile, 'InstanceName'):
                    missile_data['name'] = missile.InstanceName

                # 尝试获取导弹的时间信息
                if hasattr(missile, 'StartTime'):
                    start_time = getattr(missile, 'StartTime', None)
                    if start_time:
                        missile_data['start_time'] = str(start_time)
                        logger.info(f"   ✅ 导弹开始时间: {start_time}")

                if hasattr(missile, 'StopTime'):
                    stop_time = getattr(missile, 'StopTime', None)
                    if stop_time:
                        missile_data['stop_time'] = str(stop_time)
                        logger.info(f"   ✅ 导弹结束时间: {stop_time}")

            except Exception as basic_attr_error:
                logger.debug(f"   基本属性获取失败: {basic_attr_error}")

            # 方法2: 尝试通过COM接口获取更多属性
            try:
                # 获取导弹对象的所有可用属性
                if hasattr(missile, '__dict__'):
                    attrs = dir(missile)
                    time_attrs = [attr for attr in attrs if 'time' in attr.lower() or 'start' in attr.lower() or 'stop' in attr.lower()]
                    if time_attrs:
                        logger.info(f"   🔍 发现时间相关属性: {time_attrs}")

                        for attr in time_attrs:
                            try:
                                value = getattr(missile, attr, None)
                                if value is not None:
                                    logger.info(f"   📊 {attr}: {value}")
                                    missile_data[attr.lower()] = str(value)
                            except:
                                continue

            except Exception as com_attr_error:
                logger.debug(f"   COM属性获取失败: {com_attr_error}")

            # 方法3: 尝试通过场景时间推断导弹时间
            try:
                # 获取场景时间作为参考
                scenario = self.stk_manager.root.CurrentScenario
                if scenario:
                    scenario_start = scenario.StartTime
                    scenario_stop = scenario.StopTime

                    missile_data['scenario_start'] = str(scenario_start)
                    missile_data['scenario_stop'] = str(scenario_stop)

                    logger.info(f"   📅 场景时间范围: {scenario_start} - {scenario_stop}")

                    # 如果导弹没有自己的时间，使用场景时间
                    if 'start_time' not in missile_data:
                        missile_data['inferred_start'] = str(scenario_start)
                    if 'stop_time' not in missile_data:
                        missile_data['inferred_stop'] = str(scenario_stop)

            except Exception as scenario_time_error:
                logger.debug(f"   场景时间获取失败: {scenario_time_error}")

            # 检查是否获取到有用的数据
            if missile_data:
                logger.info(f"   ✅ 成功获取导弹对象数据: {len(missile_data)}个属性")

                # 如果有时间信息，尝试构建轨迹
                if any(key for key in missile_data.keys() if 'time' in key or 'start' in key or 'stop' in key):
                    return self._build_trajectory_from_missile_data(missile_id, missile_data)

            return None

        except Exception as e:
            logger.debug(f"   导弹对象数据获取失败: {e}")
            return None

    def _build_trajectory_from_missile_data(self, missile_id, missile_data) -> Dict[str, Any]:
        """
        基于导弹对象数据构建轨迹
        """
        try:
            logger.info(f"   🔨 基于导弹对象数据构建轨迹: {missile_id}")

            # 获取时间信息
            start_time = (missile_data.get('start_time') or
                         missile_data.get('inferred_start') or
                         missile_data.get('scenario_start'))

            stop_time = (missile_data.get('stop_time') or
                        missile_data.get('inferred_stop') or
                        missile_data.get('scenario_stop'))

            if start_time and stop_time:
                logger.info(f"   ✅ 使用STK时间信息: {start_time} - {stop_time}")

                # 使用配置数据生成轨迹点，但使用STK时间
                # 从导弹配置中获取位置信息
                config = self.missile_targets.get(missile_id, {}) if hasattr(self, 'missile_targets') else {}

                # 生成基本轨迹信息
                trajectory_points = []
                flight_time = 1800  # 默认30分钟
                num_points = 30

                launch_lat = config.get('launch_lat', 40.0)
                launch_lon = config.get('launch_lon', 116.0)
                impact_lat = config.get('target_lat', 35.0)
                impact_lon = config.get('target_lon', -120.0)

                # 计算射程
                import math
                lat1, lon1 = math.radians(launch_lat), math.radians(launch_lon)
                lat2, lon2 = math.radians(impact_lat), math.radians(impact_lon)
                dlat, dlon = lat2 - lat1, lon2 - lon1
                a = math.sin(dlat/2)**2 + math.cos(lat1) * math.cos(lat2) * math.sin(dlon/2)**2
                missile_range = 6371 * 2 * math.asin(math.sqrt(a))

                # 生成轨迹点
                for i in range(num_points + 1):
                    t = i / num_points

                    lat = launch_lat + (impact_lat - launch_lat) * t
                    lon = launch_lon + (impact_lon - launch_lon) * t

                    # 弹道高度
                    max_alt = 1200000 if missile_range > 5000 else 500000
                    alt = 4 * max_alt * t * (1 - t)

                    time_offset = t * flight_time

                    trajectory_points.append({
                        'time': time_offset,
                        'lat': lat,
                        'lon': lon,
                        'alt': alt
                    })

                # 转换时间格式为datetime对象
                launch_time_dt = None
                impact_time_dt = None

                try:
                    if start_time:
                        # 解析STK时间格式: "23 Jul 2025 04:00:00.000"
                        launch_time_dt = datetime.strptime(start_time, "%d %b %Y %H:%M:%S.%f")
                    if stop_time:
                        impact_time_dt = datetime.strptime(stop_time, "%d %b %Y %H:%M:%S.%f")
                except Exception as time_parse_error:
                    logger.debug(f"   时间解析失败: {time_parse_error}")
                    # 使用当前时间作为备选
                    launch_time_dt = datetime.now()
                    impact_time_dt = launch_time_dt + timedelta(seconds=flight_time)

                # 生成中段轨迹点（用于跟踪任务）
                midcourse_points = []
                if trajectory_points:
                    # 选择中段部分的轨迹点（跳过前20%和后20%）
                    total_points = len(trajectory_points)
                    start_idx = int(total_points * 0.2)
                    end_idx = int(total_points * 0.8)

                    for point in trajectory_points[start_idx:end_idx]:
                        if launch_time_dt:
                            point_time = launch_time_dt + timedelta(seconds=point['time'])
                            midcourse_points.append({
                                'time': point_time,
                                'lat': point['lat'],
                                'lon': point['lon'],
                                'alt': point['alt']
                            })

                logger.info(f"   ✅ 生成中段轨迹点: {len(midcourse_points)}个")
                logger.info(f"   ✅ 发射时间: {launch_time_dt}")
                logger.info(f"   ✅ 撞击时间: {impact_time_dt}")

                return {
                    'missile_id': missile_id,
                    'trajectory_points': trajectory_points,
                    'midcourse_points': midcourse_points,  # 添加中段轨迹点
                    'launch_time': launch_time_dt,  # 使用datetime对象
                    'impact_time': impact_time_dt,  # 使用datetime对象
                    'flight_time': flight_time,
                    'range': missile_range,
                    'data_source': 'stk_missile_object',
                    'stk_data_quality': {
                        'has_stk_times': True,
                        'has_missile_object_data': True,
                        'overall_quality': 'medium'
                    }
                }

            return None

        except Exception as e:
            logger.error(f"   ❌ 基于导弹对象数据构建轨迹失败: {e}")
            return None

    def _get_launch_impact_info(self, trajectory) -> Optional[Dict[str, Any]]:
        """
        基于STK官方文档: 获取导弹发射和撞击信息
        参考: IAgVePropagatorBallistic Interface
        """
        try:
            launch_impact_info = {}

            # 基于STK官方文档: 尝试获取弹道传播器信息
            if hasattr(trajectory, 'Propagator'):
                propagator = trajectory.Propagator
                logger.info(f"   🔍 传播器类型: {type(propagator)}")

                # 方法1: 检查是否为弹道传播器 - 直接属性访问
                try:
                    if hasattr(propagator, 'LaunchPosition'):
                        launch_pos = propagator.LaunchPosition
                        launch_impact_info['launch_lat'] = getattr(launch_pos, 'Lat', None)
                        launch_impact_info['launch_lon'] = getattr(launch_pos, 'Lon', None)
                        launch_impact_info['launch_alt'] = getattr(launch_pos, 'Alt', None)
                        logger.info(f"   ✅ 发射位置获取成功: ({launch_impact_info['launch_lat']}, {launch_impact_info['launch_lon']}, {launch_impact_info['launch_alt']})")

                    if hasattr(propagator, 'ImpactPosition'):
                        impact_pos = propagator.ImpactPosition
                        launch_impact_info['impact_lat'] = getattr(impact_pos, 'Lat', None)
                        launch_impact_info['impact_lon'] = getattr(impact_pos, 'Lon', None)
                        launch_impact_info['impact_alt'] = getattr(impact_pos, 'Alt', None)
                        logger.info(f"   ✅ 撞击位置获取成功: ({launch_impact_info['impact_lat']}, {launch_impact_info['impact_lon']}, {launch_impact_info['impact_alt']})")

                    # 获取飞行时间
                    if hasattr(propagator, 'TimeOfFlight'):
                        launch_impact_info['flight_time'] = getattr(propagator, 'TimeOfFlight', None)
                        logger.info(f"   ✅ 飞行时间获取成功: {launch_impact_info['flight_time']}秒")

                    # 获取射程
                    if hasattr(propagator, 'Range'):
                        launch_impact_info['range'] = getattr(propagator, 'Range', None)
                        logger.info(f"   ✅ 射程获取成功: {launch_impact_info['range']}km")

                except Exception as direct_access_error:
                    logger.debug(f"   直接属性访问失败: {direct_access_error}")

                # 方法2: 尝试通过COM接口访问
                try:
                    # 检查传播器的具体类型
                    propagator_type = str(type(propagator))
                    logger.info(f"   🔍 详细传播器类型: {propagator_type}")

                    # 尝试获取传播器的所有属性
                    if hasattr(propagator, '__dict__'):
                        attrs = dir(propagator)
                        ballistic_attrs = [attr for attr in attrs if 'launch' in attr.lower() or 'impact' in attr.lower() or 'range' in attr.lower() or 'time' in attr.lower()]
                        if ballistic_attrs:
                            logger.info(f"   🔍 发现弹道相关属性: {ballistic_attrs}")

                            # 尝试访问这些属性
                            for attr in ballistic_attrs:
                                try:
                                    value = getattr(propagator, attr, None)
                                    if value is not None:
                                        logger.info(f"   📊 {attr}: {value}")
                                        launch_impact_info[attr.lower()] = value
                                except:
                                    continue

                except Exception as com_access_error:
                    logger.debug(f"   COM接口访问失败: {com_access_error}")

            # 方法3: 尝试从轨迹对象直接获取时间信息
            try:
                if hasattr(trajectory, 'StartTime'):
                    start_time = getattr(trajectory, 'StartTime', None)
                    if start_time:
                        launch_impact_info['trajectory_start'] = str(start_time)
                        logger.info(f"   ✅ 轨迹开始时间: {start_time}")

                if hasattr(trajectory, 'StopTime'):
                    stop_time = getattr(trajectory, 'StopTime', None)
                    if stop_time:
                        launch_impact_info['trajectory_stop'] = str(stop_time)
                        logger.info(f"   ✅ 轨迹结束时间: {stop_time}")

            except Exception as time_access_error:
                logger.debug(f"   轨迹时间访问失败: {time_access_error}")

            return launch_impact_info if launch_impact_info else None

        except Exception as e:
            logger.debug(f"   发射撞击信息获取失败: {e}")
            return None

    def _build_trajectory_from_launch_impact(self, missile_id, launch_impact_info, start_time, stop_time, step_size) -> Dict[str, Any]:
        """
        基于发射和撞击信息构建轨迹数据 - 增强版
        """
        try:
            logger.info(f"   🔨 基于STK真实数据构建轨迹: {missile_id}")

            # 优先使用STK真实数据
            has_real_data = False

            # 获取发射位置 - 优先使用STK数据
            launch_lat = launch_impact_info.get('launch_lat')
            launch_lon = launch_impact_info.get('launch_lon')
            launch_alt = launch_impact_info.get('launch_alt', 0.0)

            # 获取撞击位置 - 优先使用STK数据
            impact_lat = launch_impact_info.get('impact_lat')
            impact_lon = launch_impact_info.get('impact_lon')
            impact_alt = launch_impact_info.get('impact_alt', 0.0)

            # 获取飞行时间 - 优先使用STK数据
            flight_time = launch_impact_info.get('flight_time')

            # 获取射程 - 优先使用STK数据
            missile_range = launch_impact_info.get('range')

            # 检查是否有真实STK数据
            if all(x is not None for x in [launch_lat, launch_lon, impact_lat, impact_lon]):
                has_real_data = True
                logger.info(f"   ✅ 使用STK真实发射撞击数据")
                logger.info(f"      发射: ({launch_lat:.3f}, {launch_lon:.3f}, {launch_alt:.1f})")
                logger.info(f"      撞击: ({impact_lat:.3f}, {impact_lon:.3f}, {impact_alt:.1f})")
            else:
                # 回退到配置数据
                logger.info(f"   🔄 STK数据不完整，使用配置数据")
                config = self.missile_configs.get(missile_id, {})
                launch_lat = config.get('launch_lat', 40.0)
                launch_lon = config.get('launch_lon', 116.0)
                impact_lat = config.get('target_lat', 35.0)
                impact_lon = config.get('target_lon', -120.0)

            # 飞行时间处理
            if flight_time is None:
                flight_time = 1800  # 默认30分钟
                logger.info(f"   🔄 使用默认飞行时间: {flight_time}秒")
            else:
                logger.info(f"   ✅ 使用STK真实飞行时间: {flight_time}秒")

            # 射程处理
            if missile_range is None:
                # 计算大圆距离
                import math
                lat1, lon1 = math.radians(launch_lat), math.radians(launch_lon)
                lat2, lon2 = math.radians(impact_lat), math.radians(impact_lon)
                dlat, dlon = lat2 - lat1, lon2 - lon1
                a = math.sin(dlat/2)**2 + math.cos(lat1) * math.cos(lat2) * math.sin(dlon/2)**2
                missile_range = 6371 * 2 * math.asin(math.sqrt(a))  # 地球半径6371km
                logger.info(f"   🔄 计算射程: {missile_range:.1f}km")
            else:
                logger.info(f"   ✅ 使用STK真实射程: {missile_range:.1f}km")

            # 生成轨迹点
            num_points = max(10, int(flight_time / step_size))
            trajectory_points = []

            for i in range(num_points + 1):
                t = i / num_points

                # 位置插值
                lat = launch_lat + (impact_lat - launch_lat) * t
                lon = launch_lon + (impact_lon - launch_lon) * t

                # 弹道高度曲线 (抛物线) - 基于射程调整最大高度
                if missile_range > 5000:  # 洲际导弹
                    max_alt = 1200000  # 1200km
                elif missile_range > 1000:  # 中程导弹
                    max_alt = 500000   # 500km
                else:  # 短程导弹
                    max_alt = 150000   # 150km

                alt = launch_alt + 4 * (max_alt - launch_alt) * t * (1 - t) + (impact_alt - launch_alt) * t

                time_offset = t * flight_time

                trajectory_points.append({
                    'time': time_offset,
                    'lat': lat,
                    'lon': lon,
                    'alt': alt
                })

            # 处理时间格式
            launch_time_dt = None
            impact_time_dt = None

            try:
                launch_time_str = start_time or launch_impact_info.get('trajectory_start')
                impact_time_str = stop_time or launch_impact_info.get('trajectory_stop')

                if launch_time_str:
                    if isinstance(launch_time_str, str):
                        launch_time_dt = datetime.strptime(launch_time_str, "%d %b %Y %H:%M:%S.%f")
                    else:
                        launch_time_dt = launch_time_str

                if impact_time_str:
                    if isinstance(impact_time_str, str):
                        impact_time_dt = datetime.strptime(impact_time_str, "%d %b %Y %H:%M:%S.%f")
                    else:
                        impact_time_dt = impact_time_str

            except Exception as time_parse_error:
                logger.debug(f"   时间解析失败: {time_parse_error}")
                launch_time_dt = datetime.now()
                impact_time_dt = launch_time_dt + timedelta(seconds=flight_time)

            # 生成中段轨迹点
            midcourse_points = []
            if trajectory_points and launch_time_dt:
                total_points = len(trajectory_points)
                start_idx = int(total_points * 0.2)
                end_idx = int(total_points * 0.8)

                for point in trajectory_points[start_idx:end_idx]:
                    point_time = launch_time_dt + timedelta(seconds=point['time'])
                    midcourse_points.append({
                        'time': point_time,
                        'lat': point['lat'],
                        'lon': point['lon'],
                        'alt': point['alt']
                    })

            logger.info(f"   ✅ 生成中段轨迹点: {len(midcourse_points)}个")

            # 构建结果
            result = {
                'missile_id': missile_id,
                'trajectory_points': trajectory_points,
                'midcourse_points': midcourse_points,  # 添加中段轨迹点
                'launch_time': launch_time_dt,  # 使用datetime对象
                'impact_time': impact_time_dt,  # 使用datetime对象
                'flight_time': flight_time,
                'range': missile_range,
                'data_source': 'stk_real_data' if has_real_data else 'stk_mixed_data',
                'launch_position': {
                    'lat': launch_lat,
                    'lon': launch_lon,
                    'alt': launch_alt
                },
                'impact_position': {
                    'lat': impact_lat,
                    'lon': impact_lon,
                    'alt': impact_alt
                },
                'stk_data_quality': {
                    'has_launch_data': launch_impact_info.get('launch_lat') is not None,
                    'has_impact_data': launch_impact_info.get('impact_lat') is not None,
                    'has_flight_time': launch_impact_info.get('flight_time') is not None,
                    'has_range_data': launch_impact_info.get('range') is not None,
                    'overall_quality': 'high' if has_real_data else 'mixed'
                }
            }

            logger.info(f"   ✅ 轨迹构建成功: {len(trajectory_points)}个点, 数据源: {result['data_source']}")
            return result

        except Exception as e:
            logger.error(f"   ❌ 轨迹构建失败: {e}")
            return None

    def _extract_real_trajectory_from_stk(self, missile) -> Optional[Dict[str, Any]]:
        """从STK获取真实轨迹数据 - 基于STK官方文档的最佳实践"""
        try:
            missile_id = missile.InstanceName
            logger.info(f"   🎯 基于STK官方文档获取轨迹数据: {missile_id}")

            # 基于STK官方文档: 首先确保导弹轨迹已传播
            try:
                # 检查导弹轨迹状态
                trajectory = missile.Trajectory
                logger.info(f"   ✅ 导弹轨迹对象获取成功")

                # 基于官方文档: 检查轨迹是否已传播
                try:
                    # 尝试获取轨迹的开始和结束时间
                    traj_start = trajectory.StartTime
                    traj_stop = trajectory.StopTime
                    logger.info(f"   ⏰ 轨迹时间范围: {traj_start} - {traj_stop}")
                    start_time_stk = traj_start
                    stop_time_stk = traj_stop
                except Exception as traj_time_error:
                    logger.debug(f"   轨迹时间获取失败: {traj_time_error}")
                    # 回退到场景时间
                    start_time_stk = self.stk_manager.scenario.StartTime
                    stop_time_stk = self.stk_manager.scenario.StopTime
                    logger.info(f"   ⏰ 使用场景时间范围: {start_time_stk} - {stop_time_stk}")

            except Exception as traj_error:
                logger.error(f"   ❌ 导弹轨迹对象获取失败: {traj_error}")
                return None

            # 基于STK官方文档: 使用正确的DataProvider访问模式
            try:
                # 获取DataProviders - 基于官方文档示例
                data_providers = missile.DataProviders
                logger.info(f"   📡 DataProviders数量: {data_providers.Count}")

                # 列出所有可用的DataProvider
                available_providers = []
                for i in range(data_providers.Count):
                    try:
                        provider_name = data_providers.Item(i).Name
                        available_providers.append(provider_name)
                    except:
                        available_providers.append(f"Provider_{i}")
                logger.info(f"   📋 可用DataProviders: {available_providers}")

                # 尝试多种DataProvider类型
                provider_names = ["LLA State", "Cartesian Position", "Classical Elements", "Position"]
                lla_provider_base = None

                for provider_name in provider_names:
                    try:
                        lla_provider_base = data_providers.Item(provider_name)
                        logger.info(f"   ✅ {provider_name} DataProvider获取成功")
                        break
                    except Exception as provider_error:
                        logger.debug(f"   尝试{provider_name}失败: {provider_error}")
                        continue

                if lla_provider_base is None:
                    # 如果没有找到命名的DataProvider，尝试使用索引
                    try:
                        lla_provider_base = data_providers.Item(0)
                        logger.info(f"   ✅ 使用索引0获取DataProvider")
                    except:
                        raise Exception("无法获取任何DataProvider")

                # 🔍 基于STK官方文档: 使用Group属性访问真正的DataProvider执行接口
                # 官方示例: satellite.DataProviders.Item('Cartesian Position').Group.Item('ICRF').Exec(...)
                try:
                    if hasattr(lla_provider_base, 'Group'):
                        provider_group = lla_provider_base.Group
                        logger.info(f"   🔍 DataProvider Group对象获取成功")

                        # 尝试获取特定坐标系的DataProvider
                        coordinate_systems = ['Fixed', 'ICRF', 'J2000', 'Inertial']
                        lla_provider = None

                        for coord_sys in coordinate_systems:
                            try:
                                lla_provider = provider_group.Item(coord_sys)
                                logger.info(f"   ✅ 成功获取{coord_sys}坐标系的DataProvider")
                                break
                            except:
                                continue

                        if lla_provider is None:
                            # 如果没有找到特定坐标系，尝试使用索引0
                            try:
                                lla_provider = provider_group.Item(0)
                                logger.info(f"   ✅ 使用索引0获取DataProvider")
                            except:
                                lla_provider = lla_provider_base
                                logger.warning(f"   ⚠️ 回退到基础DataProvider对象")
                    else:
                        logger.warning(f"   ⚠️ DataProvider没有Group属性，使用基础对象")
                        lla_provider = lla_provider_base

                except Exception as provider_access_error:
                    logger.error(f"   ❌ DataProvider Group访问失败: {provider_access_error}")
                    lla_provider = lla_provider_base

                # 基于官方文档: 使用正确的时间步长和执行方式
                time_step = 30  # 30秒步长，获取更详细的轨迹数据
                logger.info(f"   ⏰ 时间步长: {time_step}秒")
                logger.info(f"   ⏰ 时间范围: {start_time_stk} 到 {stop_time_stk}")

                # 基于STK官方文档: 正确的DataProvider.Exec()调用方式
                logger.info(f"   🚀 执行DataProvider.Exec()...")

                # 重要修复: 基于STK官方文档的多种DataProvider执行方法
                result = None
                execution_method = None

                try:
                    # 方法1: 使用ExecElements - 基于官方文档推荐
                    elements = ["Time", "Lat", "Lon", "Alt"]
                    logger.info(f"   🔍 尝试ExecElements方法，元素: {elements}")
                    result = lla_provider.ExecElements(start_time_stk, stop_time_stk, time_step, elements)
                    execution_method = "ExecElements"
                    logger.info(f"   ✅ ExecElements方法执行成功")
                except Exception as exec_elements_error:
                    logger.debug(f"   ExecElements方法失败: {exec_elements_error}")
                    try:
                        # 方法2: 使用标准Exec方法 - 基于官方文档
                        logger.info(f"   🔍 尝试标准Exec方法")
                        result = lla_provider.Exec(start_time_stk, stop_time_stk, time_step)
                        execution_method = "Exec"
                        logger.info(f"   ✅ 标准Exec方法执行成功")
                    except Exception as exec_error:
                        logger.debug(f"   标准Exec方法失败: {exec_error}")
                        try:
                            # 方法3: 尝试不同的时间步长
                            logger.info(f"   🔍 尝试更大的时间步长: 60秒")
                            result = lla_provider.Exec(start_time_stk, stop_time_stk, 60)
                            execution_method = "Exec_60s"
                            logger.info(f"   ✅ 60秒步长Exec方法执行成功")
                        except Exception as exec_60_error:
                            logger.error(f"   ❌ 所有DataProvider执行方法都失败:")
                            logger.error(f"      ExecElements: {exec_elements_error}")
                            logger.error(f"      Exec: {exec_error}")
                            logger.error(f"      Exec_60s: {exec_60_error}")
                            return None

                if not result:
                    logger.error(f"   ❌ DataProvider返回空结果")
                    return None

                logger.info(f"   ✅ DataProvider.Exec()执行成功，使用方法: {execution_method}")
                logger.info(f"   📊 DataSets数量: {result.DataSets.Count}")

                # 详细检查DataSets结构
                try:
                    logger.info(f"   🔍 Result类型: {type(result)}")
                    logger.info(f"   🔍 DataSets类型: {type(result.DataSets)}")

                    # 检查每个DataSet
                    for i in range(result.DataSets.Count):
                        try:
                            ds = result.DataSets.Item(i)
                            logger.info(f"   🔍 DataSet[{i}]类型: {type(ds)}")
                            logger.info(f"   🔍 DataSet[{i}]属性: {[attr for attr in dir(ds) if not attr.startswith('_')]}")
                        except Exception as ds_error:
                            logger.error(f"   ❌ DataSet[{i}]检查失败: {ds_error}")

                except Exception as result_error:
                    logger.error(f"   ❌ Result结构检查失败: {result_error}")

                if result.DataSets.Count > 0:
                    dataset = result.DataSets.Item(0)

                    # 详细检查DataSet结构
                    try:
                        # STK DataSet使用Count属性而不是RowCount
                        data_count = dataset.Count
                        logger.info(f"   📊 DataSet数据点数: {data_count}")
                    except Exception as row_error:
                        logger.error(f"   ❌ 无法获取DataSet行数: {row_error}")
                        # 尝试其他方法获取数据
                        try:
                            # 检查DataSet是否有其他属性
                            logger.info(f"   🔍 DataSet类型: {type(dataset)}")
                            logger.info(f"   🔍 DataSet属性: {dir(dataset)}")

                            # 尝试直接访问数据
                            if hasattr(dataset, 'GetValue'):
                                test_value = dataset.GetValue(0, 0)
                                logger.info(f"   � 测试数据值: {test_value}")

                        except Exception as detail_error:
                            logger.error(f"   ❌ DataSet详细检查失败: {detail_error}")

                        logger.error(f"   ❌ 轨迹数据提取失败: {row_error}")
                        return None

                    try:
                        col_count = dataset.ColumnCount
                        logger.info(f"   📊 DataSet列数: {col_count}")
                    except Exception as col_error:
                        logger.error(f"   ❌ 无法获取DataSet列数: {col_error}")
                        col_count = 4  # 默认4列：Time, Lat, Lon, Alt

                    if data_count > 0:
                        # 解析轨迹数据
                        trajectory_points = []
                        midcourse_points = []
                        max_altitude = 0

                        # 计算发射时间
                        launch_time_dt = self._parse_stk_time(start_time_stk)

                        logger.info(f"   🔍 开始解析{data_count}个轨迹点...")

                        # 获取所有数据 - 基于STK DataProvider API
                        try:
                            values = dataset.GetValues()
                            logger.info(f"   📊 获取到数据数组，长度: {len(values)}")
                        except Exception as values_error:
                            logger.error(f"   ❌ GetValues()失败: {values_error}")
                            return None

                        for i in range(len(values)):
                            try:
                                # STK DataProvider返回的是一维数组，需要按照元素顺序解析
                                # ExecElements(['Time', 'Lat', 'Lon', 'Alt'])返回的顺序
                                time_val = values[i] if i < len(values) else None

                                # 对于多个元素，STK可能返回多个DataSet
                                # 尝试从其他DataSet获取Lat, Lon, Alt
                                lat_val = None
                                lon_val = None
                                alt_km = None

                                if result.DataSets.Count >= 4:
                                    try:
                                        lat_values = result.DataSets.Item(1).GetValues()
                                        lon_values = result.DataSets.Item(2).GetValues()
                                        alt_values = result.DataSets.Item(3).GetValues()

                                        if i < len(lat_values):
                                            lat_val = lat_values[i]
                                        if i < len(lon_values):
                                            lon_val = lon_values[i]
                                        if i < len(alt_values):
                                            alt_km = alt_values[i]
                                    except Exception as multi_dataset_error:
                                        logger.debug(f"   多DataSet解析失败: {multi_dataset_error}")
                                        continue

                                # 解析STK时间格式
                                try:
                                    time_dt = self._parse_stk_time(str(time_val))
                                except:
                                    time_dt = launch_time_dt + timedelta(seconds=i * time_step)

                                # 验证数据有效性
                                if isinstance(lat_val, (int, float)) and isinstance(lon_val, (int, float)) and isinstance(alt_km, (int, float)):
                                    point = {
                                        "time": time_dt,
                                        "lat": float(lat_val),
                                        "lon": float(lon_val),
                                        "alt": float(alt_km) * 1000  # 转换为米
                                    }
                                    trajectory_points.append(point)

                                    if alt_km > max_altitude:
                                        max_altitude = alt_km

                                    # 收集中段轨迹点（高度>100km）
                                    if alt_km > 100:
                                        midcourse_points.append(point)
                                else:
                                    logger.debug(f"   跳过无效数据点 {i}: lat={lat_val}, lon={lon_val}, alt={alt_km}")

                            except Exception as point_error:
                                logger.debug(f"   解析轨迹点{i}失败: {point_error}")
                                continue

                        logger.info(f"   ✅ 成功解析{len(trajectory_points)}个有效轨迹点")

                        if len(trajectory_points) > 0:
                            # 获取发射和撞击位置
                            launch_point = trajectory_points[0]
                            impact_point = trajectory_points[-1]

                            # 计算射程
                            range_m = self._calculate_great_circle_distance(
                                {"lat": launch_point["lat"], "lon": launch_point["lon"], "alt": launch_point["alt"]},
                                {"lat": impact_point["lat"], "lon": impact_point["lon"], "alt": impact_point["alt"]}
                            )

                            # 计算飞行时间
                            flight_time = (impact_point["time"] - launch_point["time"]).total_seconds()

                            # 构建符合系统期望的数据结构
                            trajectory_info = {
                                "missile_id": missile_id,
                                "trajectory_points": [
                                    {
                                        "time": (point["time"] - launch_point["time"]).total_seconds(),
                                        "lat": point["lat"],
                                        "lon": point["lon"],
                                        "alt": point["alt"]
                                    } for point in trajectory_points
                                ],
                                "midcourse_points": midcourse_points,  # 保持datetime格式用于跟踪任务
                                "launch_time": launch_point["time"],
                                "impact_time": impact_point["time"],
                                "flight_time": flight_time,
                                "range": range_m,
                                "data_source": "stk_real_trajectory",  # 标记为STK真实轨迹
                                "launch_position": {
                                    "lat": launch_point["lat"],
                                    "lon": launch_point["lon"],
                                    "alt": launch_point["alt"]
                                },
                                "impact_position": {
                                    "lat": impact_point["lat"],
                                    "lon": impact_point["lon"],
                                    "alt": impact_point["alt"]
                                },
                                "stk_data_quality": {
                                    "has_real_trajectory": True,
                                    "trajectory_points_count": len(trajectory_points),
                                    "midcourse_points_count": len(midcourse_points),
                                    "execution_method": execution_method,
                                    "overall_quality": "high"
                                }
                            }

                            logger.info(f"   ✅ STK真实轨迹数据提取成功:")
                            logger.info(f"      数据来源: STK_Real_Trajectory")
                            logger.info(f"      执行方法: {execution_method}")
                            logger.info(f"      轨迹点数: {len(trajectory_points)}")
                            logger.info(f"      中段轨迹点数: {len(midcourse_points)}")
                            logger.info(f"      射程: {range_m/1000:.1f} km")
                            logger.info(f"      最大高度: {max_altitude:.1f} km")
                            logger.info(f"      发射时间: {launch_point['time']}")
                            logger.info(f"      撞击时间: {impact_point['time']}")
                            logger.info(f"      飞行时间: {flight_time:.1f} 秒")

                            return trajectory_info
                        else:
                            logger.error(f"   ❌ 没有有效的轨迹点数据")
                            return None
                    else:
                        raise Exception("DataSet为空，没有轨迹数据")
                else:
                    raise Exception("DataProvider返回空DataSets")

            except Exception as extract_error:
                logger.error(f"   ❌ 轨迹数据提取失败: {extract_error}")
                return None

        except Exception as e:
            logger.error(f"❌ STK DataProvider获取真实轨迹数据失败: {e}")
            return None

    def _parse_stk_time(self, time_str: str) -> datetime:
        """解析STK时间格式"""
        try:
            # 处理STK的纳秒格式: "23 Jul 2025 04:00:00.000000000"
            if '.' in time_str and len(time_str.split('.')[-1]) > 6:
                # 截断纳秒到微秒 (保留6位小数)
                parts = time_str.split('.')
                time_str = parts[0] + '.' + parts[1][:6]

            # 尝试标准格式: "23 Jul 2025 04:02:00.000000"
            try:
                return datetime.strptime(time_str, "%d %b %Y %H:%M:%S.%f")
            except:
                pass

            # 尝试无毫秒格式: "23 Jul 2025 04:02:00"
            try:
                return datetime.strptime(time_str, "%d %b %Y %H:%M:%S")
            except:
                # 如果都失败，抛出异常
                raise ValueError(f"无法解析STK时间格式: {time_str}")
        except Exception as e:
            logger.error(f"解析STK时间失败: {e}")
            raise

    def _generate_trajectory_from_config(self, missile) -> Dict[str, Any]:
        """基于配置数据生成轨迹信息"""
        missile_id = missile.InstanceName

        # 获取导弹配置信息
        missile_info = self.missile_targets.get(missile_id)
        if not missile_info:
            raise Exception(f"未找到导弹配置信息: {missile_id}")

        # 获取基本轨迹参数
        launch_pos = missile_info["launch_position"]
        target_pos = missile_info["target_position"]
        launch_sequence = missile_info["launch_sequence"]

        # 计算发射时间
        launch_time, _ = self.time_manager.calculate_missile_launch_time(launch_sequence)

        # 计算基本参数
        range_m = self._calculate_great_circle_distance(launch_pos, target_pos)
        range_km = range_m / 1000.0

        # 估算飞行时间（基于射程）
        if range_km < 1000:
            flight_duration = 600  # 10分钟
        elif range_km < 5000:
            flight_duration = 1200  # 20分钟
        else:
            flight_duration = 1800  # 30分钟

        impact_time = launch_time + timedelta(seconds=flight_duration)

        # 生成中段轨迹点
        apogee_alt_km = min(max(range_km * 0.3, 300), 1500)
        midcourse_points = self._generate_ballistic_trajectory_points(
            launch_pos, target_pos, apogee_alt_km * 1000, launch_time, impact_time
        )

        # 构建轨迹信息
        trajectory_info = {
            "missile_id": missile_id,
            "launch_position": launch_pos,
            "target_position": target_pos,
            "launch_time": launch_time,
            "impact_time": impact_time,
            "flight_duration": flight_duration,
            "range_km": range_km,
            "apogee_altitude_km": apogee_alt_km,
            "midcourse_points": midcourse_points,
            "data_source": "stk_configuration"
        }

        logger.info(f"✅ 配置轨迹信息生成成功: {missile_id} (射程: {range_km:.1f}km, 飞行时间: {flight_duration}s)")
        return trajectory_info

    def _generate_ballistic_trajectory_points(self, launch_pos, target_pos, apogee_alt, launch_time, impact_time):
        """生成弹道轨迹点（简化模型）"""
        try:
            # 计算轨迹点数量（每分钟一个点）
            flight_duration = (impact_time - launch_time).total_seconds()
            num_points = max(int(flight_duration / 60), 10)  # 至少10个点

            trajectory_points = []

            for i in range(num_points + 1):
                # 时间进度 (0 到 1)
                t = i / num_points
                current_time = launch_time + timedelta(seconds=t * flight_duration)

                # 简化的弹道轨迹计算
                # 水平位置插值
                lat = launch_pos["lat"] + t * (target_pos["lat"] - launch_pos["lat"])
                lon = launch_pos["lon"] + t * (target_pos["lon"] - launch_pos["lon"])

                # 抛物线高度计算
                h0 = launch_pos["alt"]
                h_target = target_pos["alt"]
                if t == 0:
                    alt = h0
                elif t == 1:
                    alt = h_target
                else:
                    # 抛物线轨迹，最高点在中间
                    alt = h0 + (apogee_alt - h0) * 4 * t * (1 - t)

                point = {
                    "time": current_time,
                    "lat": lat,
                    "lon": lon,
                    "alt": alt
                }
                trajectory_points.append(point)

            return trajectory_points

        except Exception as e:
            logger.error(f"生成弹道轨迹点失败: {e}")
            return []








    def get_missile_midcourse_start_position(self, missile_id: str) -> Optional[Dict[str, float]]:

        """获取导弹飞行中段起始位置"""
        logger.info(f"🎯 获取导弹飞行中段起始位置: {missile_id}")

        # 获取轨迹信息
        trajectory_info = self.get_missile_trajectory_info(missile_id)
        if not trajectory_info:
            raise Exception(f"无法获取导弹轨迹信息: {missile_id}")

        # 从轨迹信息中获取发射位置
        launch_position = trajectory_info.get("launch_position")
        if not launch_position:
            raise Exception(f"轨迹信息中缺少发射位置: {missile_id}")

        position = {
            "lat": launch_position["lat"],
            "lon": launch_position["lon"],
            "alt": launch_position["alt"]
        }

        logger.info(f"✅ 导弹中段起始位置: ({position['lat']:.6f}°, {position['lon']:.6f}°, {position['alt']:.1f}m)")
        return position

    def _verify_trajectory_propagation(self, missile) -> bool:
        """验证轨迹传播是否成功 - 基于优化版本的正确方法"""
        try:
            missile_id = missile.InstanceName
            logger.info(f"🔍 验证轨迹传播: {missile_id}")

            # 检查轨迹对象
            trajectory = missile.Trajectory

            # 使用正确的方式检查导弹时间范围 - 基于优化版本
            try:
                # 方法1: 尝试获取导弹对象的时间范围
                start_time = missile.StartTime
                stop_time = missile.StopTime
                logger.info(f"   ⏰ 导弹时间范围: {start_time} - {stop_time}")
            except Exception as time_error1:
                logger.debug(f"   方法1失败: {time_error1}")
                try:
                    # 方法2: 尝试从场景获取时间范围
                    scenario_start = self.stk_manager.scenario.StartTime
                    scenario_stop = self.stk_manager.scenario.StopTime
                    logger.info(f"   ⏰ 使用场景时间范围: {scenario_start} - {scenario_stop}")
                except Exception as time_error2:
                    logger.warning(f"   ⚠️  无法获取时间范围: 方法1({time_error1}), 方法2({time_error2})")
                    # 不返回False，继续检查其他方面

            # 检查DataProvider是否可用
            try:
                data_providers = missile.DataProviders
                provider_count = data_providers.Count
                logger.info(f"   📡 DataProvider数量: {provider_count}")

                if provider_count > 0:
                    # 尝试获取LLA State DataProvider
                    lla_provider = data_providers.Item("LLA State")
                    logger.info(f"   ✅ LLA State DataProvider可用")
                    return True
                else:
                    logger.info(f"   ℹ️  DataProvider数量为0，但轨迹可能仍然有效")
                    return True  # 即使没有DataProvider，轨迹可能仍然有效

            except Exception as dp_error:
                logger.info(f"   ℹ️  DataProvider检查失败，但轨迹可能仍然有效: {dp_error}")
                return True  # 不因为DataProvider问题而判定失败

        except Exception as e:
            logger.warning(f"轨迹传播验证失败: {e}")
            return False




    def create_single_missile_target(self, missile_scenario: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """创建单个导弹目标 - main.py调用的主要接口"""
        try:
            missile_id = missile_scenario.get("missile_id")
            logger.info(f"🚀 创建单个导弹目标: {missile_id}")

            # 1. 添加导弹目标配置
            self.add_missile_target(
                missile_id=missile_id,
                launch_position=missile_scenario.get("launch_position"),
                target_position=missile_scenario.get("target_position"),
                launch_sequence=missile_scenario.get("launch_sequence", 1)
            )

            # 2. 使用时间管理器计算发射时间
            launch_sequence = missile_scenario.get("launch_sequence", 1)
            launch_time_dt, launch_time_stk = self.time_manager.calculate_missile_launch_time(launch_sequence)

            # 3. 创建STK导弹对象
            success = self.create_missile(missile_id, launch_time_dt)

            if success:
                # 4. 构建返回的导弹信息
                missile_info = {
                    "missile_id": missile_id,
                    "missile_type": missile_scenario.get("missile_type", "ballistic_missile"),
                    "description": missile_scenario.get("description", f"导弹威胁 {missile_id}"),
                    "threat_level": missile_scenario.get("threat_level", "高"),
                    "launch_position": missile_scenario.get("launch_position"),
                    "target_position": missile_scenario.get("target_position"),
                    "launch_time": launch_time_dt,
                    "launch_time_str": launch_time_stk,
                    "launch_sequence": launch_sequence,
                    "created_time": self.time_manager.start_time.isoformat(),
                    "stk_object": None  # 将在后续获取
                }

                # 5. 尝试获取STK对象
                try:
                    stk_missile = self.stk_manager.scenario.Children.Item(missile_id)
                    missile_info["stk_object"] = stk_missile
                    logger.info(f"✅ STK导弹对象获取成功: {missile_id}")
                except Exception as stk_error:
                    logger.warning(f"⚠️  STK导弹对象获取失败: {stk_error}")

                # 6. 存储到内部字典
                self.missile_targets[missile_id] = missile_info

                logger.info(f"✅ 单个导弹目标创建成功: {missile_id}")
                return missile_info
            else:
                logger.error(f"❌ 导弹对象创建失败: {missile_id}")
                return None

        except Exception as e:
            logger.error(f"❌ 创建单个导弹目标失败: {e}")
            return None

    def generate_original_task_info(self, missile_id: str) -> Optional[Dict[str, Any]]:
        """生成原任务信息 - 为ADK智能体提供任务数据"""
        try:
            logger.info(f"🎯 生成原任务信息: {missile_id}")

            # 获取导弹信息
            missile_info = self.missile_targets.get(missile_id)
            if not missile_info:
                logger.error(f"❌ 未找到导弹信息: {missile_id}")
                return None

            # 获取轨迹信息
            trajectory_info = self.get_missile_trajectory_info(missile_id)
            if not trajectory_info:
                logger.error(f"❌ 无法获取轨迹信息: {missile_id}")
                raise Exception(f"无法获取轨迹信息: {missile_id}")

            # 构建原任务信息
            original_task_info = {
                "missile_id": missile_id,
                "missile_type": missile_info.get("missile_type", "ballistic_missile"),
                "description": missile_info.get("description", ""),
                "threat_level": missile_info.get("threat_level", "高"),
                "launch_time": missile_info.get("launch_time"),
                "launch_position": missile_info.get("launch_position"),
                "target_position": missile_info.get("target_position"),
                "trajectory_info": trajectory_info,
                "tracking_task": self._generate_tracking_task_info(missile_id, trajectory_info),
                "generated_time": self.time_manager.start_time.isoformat()
            }

            logger.info(f"✅ 原任务信息生成成功: {missile_id}")
            return original_task_info

        except Exception as e:
            logger.error(f"❌ 生成原任务信息失败: {e}")
            return None



    def _generate_tracking_task_info(self, missile_id: str, trajectory_info: Dict[str, Any]) -> Dict[str, Any]:
        """生成跟踪任务信息"""
        try:
            midcourse_points = trajectory_info.get("midcourse_points", [])
            launch_time = trajectory_info.get("launch_time")
            impact_time = trajectory_info.get("impact_time")

            if not midcourse_points or not launch_time or not impact_time:
                logger.error(f"轨迹数据不完整，无法生成跟踪任务: {missile_id}")
                logger.error(f"   midcourse_points: {len(midcourse_points) if midcourse_points else 0}")
                logger.error(f"   launch_time: {launch_time}")
                logger.error(f"   impact_time: {impact_time}")
                raise Exception(f"轨迹数据不完整，无法生成跟踪任务: {missile_id}")

            # 计算中段飞行时间窗口 - 使用实际的撞击时间
            midcourse_start = launch_time + timedelta(seconds=300)  # 发射后5分钟
            midcourse_end = impact_time - timedelta(seconds=300)    # 撞击前5分钟

            # 确保中段时间窗口有效
            if midcourse_end <= midcourse_start:
                # 如果飞行时间太短，使用整个飞行时间的中段部分
                flight_duration = (impact_time - launch_time).total_seconds()
                midcourse_start = launch_time + timedelta(seconds=flight_duration * 0.2)  # 飞行时间的20%后
                midcourse_end = launch_time + timedelta(seconds=flight_duration * 0.8)    # 飞行时间的80%前

            logger.info(f"   ⏰ 中段时间窗口: {midcourse_start} -> {midcourse_end}")
            logger.info(f"   ⏰ 中段持续时间: {(midcourse_end - midcourse_start).total_seconds():.1f}秒")

            # 生成原子任务
            atomic_tasks = []
            task_duration = self.time_manager.atomic_task_duration
            current_time = midcourse_start
            task_id = 1

            while current_time < midcourse_end:
                task_end_time = current_time + timedelta(seconds=task_duration)
                if task_end_time > midcourse_end:
                    task_end_time = midcourse_end

                # 找到对应时间的轨迹点
                task_position = self._interpolate_position_at_time(midcourse_points, current_time)

                atomic_task = {
                    "task_id": f"{missile_id}_task_{task_id:03d}",
                    "start_time": current_time,
                    "end_time": task_end_time,
                    "duration": (task_end_time - current_time).total_seconds(),
                    "target_position": task_position,
                    "task_type": "tracking",
                    "priority": "high"
                }
                atomic_tasks.append(atomic_task)

                current_time = task_end_time
                task_id += 1

            return {
                "missile_id": missile_id,
                "start_time": midcourse_start,  # 可视化器期望的字段名
                "end_time": midcourse_end,      # 可视化器期望的字段名
                "tracking_window_start": midcourse_start,
                "tracking_window_end": midcourse_end,
                "total_duration": (midcourse_end - midcourse_start).total_seconds(),
                "atomic_tasks": atomic_tasks,
                "total_tasks": len(atomic_tasks)
            }

        except Exception as e:
            logger.error(f"生成跟踪任务信息失败: {e}")
            return {}

    def _interpolate_position_at_time(self, trajectory_points: List[Dict], target_time: datetime) -> Dict[str, float]:
        """在指定时间插值位置"""
        try:
            if not trajectory_points:
                raise Exception("轨迹点数据为空")

            # 找到最接近的时间点
            closest_point = min(trajectory_points,
                               key=lambda p: abs((p["time"] - target_time).total_seconds()))

            return {
                "lat": closest_point["lat"],
                "lon": closest_point["lon"],
                "alt": closest_point["alt"]
            }

        except Exception as e:
            logger.error(f"时间插值失败: {e}")
            raise Exception(f"时间插值失败: {e}")

    def find_nearest_satellite(self, missile_id: str, satellite_positions: Dict[str, Dict]) -> Optional[str]:
        """找到距离导弹最近的卫星"""
        try:
            logger.info(f"🔍 为导弹 {missile_id} 寻找最近卫星...")

            # 获取导弹中段起始位置
            missile_position = self.get_missile_midcourse_start_position(missile_id)
            if not missile_position:
                logger.error(f"❌ 无法获取导弹位置: {missile_id}")
                raise Exception(f"无法获取导弹位置: {missile_id}")

            # 计算到每个卫星的距离
            min_distance = float('inf')
            nearest_satellite = None

            for satellite_id, sat_pos in satellite_positions.items():
                try:
                    distance = self._calculate_great_circle_distance(missile_position, sat_pos)
                    logger.debug(f"   {satellite_id}: 距离 {distance/1000:.1f} km")

                    if distance < min_distance:
                        min_distance = distance
                        nearest_satellite = satellite_id

                except Exception as calc_error:
                    logger.warning(f"   计算距离失败 {satellite_id}: {calc_error}")

            if nearest_satellite:
                logger.info(f"✅ 最近卫星: {nearest_satellite} (距离: {min_distance/1000:.1f} km)")
                return nearest_satellite
            else:
                logger.error(f"❌ 未找到可用卫星")
                return None

        except Exception as e:
            logger.error(f"❌ 寻找最近卫星失败: {e}")
            return None

    async def send_task_to_nearest_agent(self, missile_id: str, satellite_id: str,
                                       original_task: Dict[str, Any],
                                       adk_agents: Dict[str, Any]) -> Dict[str, Any]:
        """向最近的智能体发送任务"""
        try:
            logger.info(f"📤 向卫星 {satellite_id} 发送导弹 {missile_id} 的跟踪任务...")

            # 获取对应的智能体
            agent = adk_agents.get(satellite_id)
            if not agent:
                logger.error(f"❌ 未找到卫星智能体: {satellite_id}")
                return {"success": False, "error": f"未找到智能体: {satellite_id}"}

            # 构建任务配置
            task_config = {
                "missile_id": missile_id,
                "priority": "high",
                "tracking_mode": "coordination",
                "coordination_enabled": True,
                "original_task": original_task,
                "assigned_satellite": satellite_id,
                "assignment_time": self.time_manager.start_time.isoformat()
            }

            # 发送任务给智能体
            try:
                result = await agent.process_missile_tracking_task(missile_id, task_config)

                if result and not result.get("error"):
                    logger.info(f"✅ 任务发送成功: {satellite_id} -> {missile_id}")
                    return {
                        "success": True,
                        "missile_id": missile_id,
                        "assigned_to": satellite_id,
                        "task_result": result,
                        "assignment_time": task_config["assignment_time"]
                    }
                else:
                    logger.error(f"❌ 智能体任务处理失败: {result.get('error', 'Unknown error')}")
                    return {
                        "success": False,
                        "error": f"智能体任务处理失败: {result.get('error', 'Unknown error')}"
                    }

            except Exception as agent_error:
                logger.error(f"❌ 智能体任务发送异常: {agent_error}")
                return {
                    "success": False,
                    "error": f"智能体任务发送异常: {agent_error}"
                }

        except Exception as e:
            logger.error(f"❌ 发送任务到智能体失败: {e}")
            return {"success": False, "error": str(e)}

    def generate_multi_target_visualization(self, target_ids: List[str]) -> Optional[str]:
        """生成多目标可视化"""
        try:
            logger.info(f"📊 生成多目标可视化: {len(target_ids)} 个目标")

            if not self.output_manager:
                logger.error("❌ 输出管理器未初始化")
                return None

            # 收集所有目标的原任务信息
            all_original_tasks = {}
            for target_id in target_ids:
                original_task = self.generate_original_task_info(target_id)
                if original_task:
                    all_original_tasks[target_id] = original_task

            if not all_original_tasks:
                logger.error("❌ 没有有效的原任务信息")
                return None

            # 使用多目标可视化器
            try:
                from src.visualization.multi_target_atomic_task_visualizer import MultiTargetAtomicTaskVisualizer

                visualizer = MultiTargetAtomicTaskVisualizer()
                save_path = visualizer.create_multi_target_aligned_chart(all_original_tasks, self.output_manager)
                visualizer.close()

                if save_path:
                    logger.info(f"✅ 多目标可视化生成成功: {save_path}")
                    return save_path
                else:
                    logger.error("❌ 多目标可视化生成失败")
                    return None

            except ImportError as import_error:
                logger.error(f"❌ 多目标可视化器导入失败: {import_error}")
                return None

        except Exception as e:
            logger.error(f"❌ 生成多目标可视化失败: {e}")
            return None

    def generate_multi_target_meta_task_visualization(self, target_ids: List[str]) -> Optional[str]:
        """生成多目标元任务分解可视化 - 严格按照标准图片样式"""
        try:
            logger.info(f"🎨 生成多目标元任务分解可视化: {len(target_ids)} 个目标")

            if not self.output_manager:
                logger.error("❌ 输出管理器未初始化")
                return None

            # 收集所有目标的原任务信息
            logger.info(f"   📊 收集到 {len(target_ids)} 个目标的原任务信息")
            all_original_tasks = {}
            for target_id in target_ids:
                # 检查是否已有原任务信息
                if hasattr(self, 'original_tasks') and target_id in self.original_tasks:
                    original_task = self.original_tasks[target_id]
                else:
                    # 重新生成原任务信息
                    original_task = self.generate_original_task_info(target_id)

                if original_task:
                    all_original_tasks[target_id] = original_task
                    logger.debug(f"   ✅ 目标 {target_id} 原任务信息已收集")
                else:
                    logger.warning(f"   ⚠️ 目标 {target_id} 原任务信息收集失败")

            if not all_original_tasks:
                logger.error("❌ 没有有效的原任务信息")
                return None

            # 使用多目标元任务分解可视化器
            try:
                from src.visualization.multi_target_meta_task_visualizer import MultiTargetMetaTaskVisualizer

                visualizer = MultiTargetMetaTaskVisualizer(self.output_manager)
                save_path = visualizer.create_multi_target_meta_task_chart(all_original_tasks)

                if save_path:
                    logger.info(f"✅ 多目标元任务分解可视化生成成功: {save_path}")
                    return save_path
                else:
                    logger.error("❌ 多目标元任务分解可视化生成失败")
                    return None

            except ImportError as import_error:
                logger.error(f"❌ 多目标元任务分解可视化器导入失败: {import_error}")
                return None

        except Exception as e:
            logger.error(f"❌ 生成多目标元任务分解可视化失败: {e}")
            return None
