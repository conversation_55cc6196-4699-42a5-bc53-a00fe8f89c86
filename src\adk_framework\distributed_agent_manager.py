#!/usr/bin/env python3
"""
分布式智能体管理器 V1.0
管理多个ADK智能体的协调和通信
融合到V1.0版本
"""

import logging
import asyncio
import uuid
from datetime import datetime
from typing import Dict, List, Any, Optional
from concurrent.futures import ThreadPoolExecutor

logger = logging.getLogger(__name__)

class DistributedAgentManager:
    """
    分布式智能体管理器 V1.0
    负责管理多个ADK智能体的协调、通信和任务分配
    """
    
    def __init__(self, max_workers: int = 10):
        """
        初始化分布式智能体管理器
        
        Args:
            max_workers: 最大工作线程数
        """
        self.agents = {}  # {agent_id: agent_instance}
        self.agent_metadata = {}  # {agent_id: metadata}
        self.message_queue = asyncio.Queue()
        self.executor = ThreadPoolExecutor(max_workers=max_workers)
        
        # 管理器状态
        self.manager_state = {
            "total_agents": 0,
            "active_agents": 0,
            "total_messages": 0,
            "total_tasks": 0,
            "created_time": datetime.now().isoformat()
        }
        
        logger.info("🌐 分布式智能体管理器V1.0初始化完成")
    
    async def add_agent(self, agent) -> str:
        """
        添加智能体到管理器
        
        Args:
            agent: 智能体实例
            
        Returns:
            str: 智能体ID
        """
        try:
            agent_id = f"agent_{uuid.uuid4().hex[:8]}"
            
            # 添加智能体
            self.agents[agent_id] = agent
            
            # 添加元数据
            self.agent_metadata[agent_id] = {
                "agent_id": agent_id,
                "satellite_id": getattr(agent, 'satellite_id', 'Unknown'),
                "capabilities": getattr(agent, 'config', {}).get('capabilities', []),
                "added_time": datetime.now().isoformat(),
                "status": "active"
            }
            
            # 更新统计
            self.manager_state["total_agents"] += 1
            self.manager_state["active_agents"] += 1
            
            logger.info(f"✅ 智能体 {agent_id} 添加成功 (卫星: {getattr(agent, 'satellite_id', 'Unknown')})")
            
            return agent_id
            
        except Exception as e:
            logger.error(f"❌ 添加智能体失败: {e}")
            return ""
    
    async def remove_agent(self, agent_id: str) -> bool:
        """
        移除智能体
        
        Args:
            agent_id: 智能体ID
            
        Returns:
            bool: 移除是否成功
        """
        try:
            if agent_id in self.agents:
                del self.agents[agent_id]
                
                if agent_id in self.agent_metadata:
                    self.agent_metadata[agent_id]["status"] = "removed"
                    self.agent_metadata[agent_id]["removed_time"] = datetime.now().isoformat()
                
                self.manager_state["active_agents"] -= 1
                
                logger.info(f"✅ 智能体 {agent_id} 移除成功")
                return True
            else:
                logger.warning(f"⚠️  智能体 {agent_id} 不存在")
                return False
                
        except Exception as e:
            logger.error(f"❌ 移除智能体失败: {e}")
            return False
    
    async def broadcast_message(self, message: Dict[str, Any], 
                              sender_id: str = None) -> Dict[str, Any]:
        """
        广播消息给所有智能体
        
        Args:
            message: 消息内容
            sender_id: 发送者ID
            
        Returns:
            Dict[str, Any]: 广播结果
        """
        try:
            logger.info(f"📢 广播消息: {message.get('type', 'unknown')}")
            
            broadcast_results = {}
            
            for agent_id, agent in self.agents.items():
                if agent_id != sender_id:  # 不发送给自己
                    try:
                        # 这里可以实现具体的消息发送逻辑
                        # 目前使用模拟方式
                        broadcast_results[agent_id] = {
                            "success": True,
                            "timestamp": datetime.now().isoformat()
                        }
                    except Exception as e:
                        broadcast_results[agent_id] = {
                            "success": False,
                            "error": str(e)
                        }
            
            self.manager_state["total_messages"] += 1
            
            return {
                "success": True,
                "message_type": message.get('type', 'unknown'),
                "total_recipients": len(broadcast_results),
                "successful_sends": sum(1 for r in broadcast_results.values() if r.get('success')),
                "results": broadcast_results
            }
            
        except Exception as e:
            logger.error(f"❌ 广播消息失败: {e}")
            return {"success": False, "error": str(e)}
    
    async def send_message(self, sender_id: str, recipient_id: str, 
                          message: Dict[str, Any]) -> bool:
        """
        发送点对点消息
        
        Args:
            sender_id: 发送者ID
            recipient_id: 接收者ID
            message: 消息内容
            
        Returns:
            bool: 发送是否成功
        """
        try:
            if recipient_id not in self.agents:
                logger.warning(f"⚠️  接收者 {recipient_id} 不存在")
                return False
            
            # 这里可以实现具体的点对点消息发送逻辑
            # 目前使用模拟方式
            logger.info(f"📤 {sender_id} -> {recipient_id}: {message.get('type', 'message')}")
            
            self.manager_state["total_messages"] += 1
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 发送消息失败: {e}")
            return False
    
    async def process_target(self, target_info: Dict[str, Any]) -> Dict[str, Any]:
        """
        分布式处理目标
        
        Args:
            target_info: 目标信息
            
        Returns:
            Dict[str, Any]: 处理结果
        """
        try:
            target_id = target_info.get("target_id", "Unknown")
            logger.info(f"🎯 分布式处理目标: {target_id}")
            
            if not self.agents:
                logger.error("❌ 没有可用的智能体")
                return {"success": False, "error": "没有可用的智能体"}
            
            # 并行处理：所有智能体同时处理目标
            tasks = []
            for agent_id, agent in self.agents.items():
                if hasattr(agent, 'receive_missile_target'):
                    task = asyncio.create_task(
                        agent.receive_missile_target(target_info)
                    )
                    tasks.append((agent_id, task))
            
            # 等待所有任务完成
            results = {}
            successful_count = 0
            
            for agent_id, task in tasks:
                try:
                    result = await task
                    results[agent_id] = result
                    if result.get("success", False):
                        successful_count += 1
                except Exception as e:
                    logger.error(f"❌ 智能体 {agent_id} 处理目标失败: {e}")
                    results[agent_id] = {"success": False, "error": str(e)}
            
            self.manager_state["total_tasks"] += 1
            
            # 计算成功率
            success_rate = (successful_count / len(tasks)) * 100 if tasks else 0
            
            logger.info(f"✅ 目标处理完成:")
            logger.info(f"   目标ID: {target_id}")
            logger.info(f"   总智能体: {len(tasks)}")
            logger.info(f"   成功处理: {successful_count}")
            logger.info(f"   成功率: {success_rate:.1f}%")
            
            return {
                "success": True,
                "target_id": target_id,
                "total_agents": len(tasks),
                "successful_agents": successful_count,
                "success_rate": success_rate,
                "results": results
            }
            
        except Exception as e:
            logger.error(f"❌ 分布式处理目标失败: {e}")
            return {"success": False, "error": str(e)}
    
    def get_agent_by_satellite(self, satellite_id: str):
        """根据卫星ID获取智能体"""
        for agent_id, agent in self.agents.items():
            if hasattr(agent, 'satellite_id') and agent.satellite_id == satellite_id:
                return agent
        return None
    
    def get_agents_by_capability(self, capability: str) -> List:
        """根据能力获取智能体列表"""
        agents = []
        for agent_id, agent in self.agents.items():
            if hasattr(agent, 'config'):
                capabilities = agent.config.get('capabilities', [])
                if capability in capabilities:
                    agents.append(agent)
        return agents
    
    def get_manager_statistics(self) -> Dict[str, Any]:
        """获取管理器统计信息"""
        stats = self.manager_state.copy()
        
        # 添加实时统计
        stats.update({
            "current_active_agents": len(self.agents),
            "agent_list": list(self.agents.keys()),
            "capabilities_summary": self._get_capabilities_summary()
        })
        
        return stats
    
    def _get_capabilities_summary(self) -> Dict[str, int]:
        """获取能力统计摘要"""
        capabilities_count = {}
        
        for agent in self.agents.values():
            if hasattr(agent, 'config'):
                capabilities = agent.config.get('capabilities', [])
                for capability in capabilities:
                    capabilities_count[capability] = capabilities_count.get(capability, 0) + 1
        
        return capabilities_count
    
    async def coordinate_agents(self, coordination_task: Dict[str, Any]) -> Dict[str, Any]:
        """
        协调智能体执行任务
        
        Args:
            coordination_task: 协调任务信息
            
        Returns:
            Dict[str, Any]: 协调结果
        """
        try:
            task_type = coordination_task.get("type", "unknown")
            logger.info(f"🤝 开始智能体协调: {task_type}")
            
            # 根据任务类型选择合适的智能体
            if task_type == "icbm_detection":
                agents = self.get_agents_by_capability("icbm_detection")
            elif task_type == "surveillance":
                agents = self.get_agents_by_capability("surveillance")
            else:
                agents = list(self.agents.values())
            
            if not agents:
                return {"success": False, "error": f"没有找到适合的智能体处理 {task_type}"}
            
            # 执行协调任务
            coordination_results = {}
            for i, agent in enumerate(agents):
                agent_id = getattr(agent, 'satellite_id', f'agent_{i}')
                try:
                    # 这里可以实现具体的协调逻辑
                    # 目前使用模拟方式
                    coordination_results[agent_id] = {
                        "success": True,
                        "role": "participant",
                        "timestamp": datetime.now().isoformat()
                    }
                except Exception as e:
                    coordination_results[agent_id] = {
                        "success": False,
                        "error": str(e)
                    }
            
            successful_coordination = sum(1 for r in coordination_results.values() if r.get('success'))
            
            logger.info(f"✅ 智能体协调完成:")
            logger.info(f"   任务类型: {task_type}")
            logger.info(f"   参与智能体: {len(agents)}")
            logger.info(f"   成功协调: {successful_coordination}")
            
            return {
                "success": True,
                "task_type": task_type,
                "total_agents": len(agents),
                "successful_coordination": successful_coordination,
                "results": coordination_results
            }
            
        except Exception as e:
            logger.error(f"❌ 智能体协调失败: {e}")
            return {"success": False, "error": str(e)}
    
    async def shutdown(self):
        """关闭管理器"""
        try:
            logger.info("🔄 关闭分布式智能体管理器")
            
            # 清理资源
            self.agents.clear()
            self.agent_metadata.clear()
            
            # 关闭线程池
            self.executor.shutdown(wait=True)
            
            logger.info("✅ 分布式智能体管理器已关闭")
            
        except Exception as e:
            logger.error(f"❌ 关闭管理器失败: {e}")
