# ICBM预警系统 v1.0

## 系统概述

ICBM预警系统是一个基于STK和ADK框架的洲际弹道导弹预警系统，具备以下核心功能：

- Walker星座卫星部署
- 传感器载荷配置
- ADK多智能体协调
- 导弹威胁检测
- 可见性计算
- 甘特图可视化

## 系统架构

```
icbm_warning_system_v1.0/
├── main.py                     # 主程序入口
├── config/
│   └── constellation_config.yaml  # 星座配置文件
├── src/
│   ├── adk_framework/          # ADK智能体框架
│   ├── stk_interface/          # STK接口模块
│   ├── utils/                  # 工具模块
│   └── visualization/          # 可视化模块
├── requirements.txt            # 依赖包列表
└── README.md                   # 说明文档
```

## 核心功能

### 1. Walker星座部署
- 按照配置文件创建3×3 Walker星座
- 自动设置轨道参数
- 卫星传感器配置

### 2. ADK多智能体系统
- 9个智能体并行协调
- 基于Google ADK框架
- 实时任务处理

### 3. 导弹威胁检测
- ICBM弹道计算
- 轨迹预测
- 威胁评估

### 4. 可见性分析
- 卫星-导弹可见性计算
- 访问间隔分析
- 甘特图生成

## 运行要求

### 环境要求
- Python 3.8+
- STK 12+
- Windows 10/11
- Conda环境管理

### 依赖包
- google-adk
- pyyaml
- matplotlib
- numpy
- pywin32

## 安装和运行

### 1. 环境准备
```bash
# 创建conda环境
conda create -n adk python=3.8
conda activate adk

# 安装依赖
pip install -r requirements.txt
```

### 2. 运行系统
```bash
# 激活环境并运行
conda activate adk
python main.py
```

## 系统特性

### ✅ 已修复的问题
- ADK FunctionTool调用修复
- 文件保存路径统一管理
- STK时间格式解析修复
- 中文字体显示修复
- 传感器清理逻辑

### ✅ 系统性能
- 100%成功率（7/7阶段）
- 9个ADK智能体，100%任务成功率
- 统一输出目录管理
- 157,404字节甘特图生成

## 输出文件

系统运行后会在`test_outputs/`目录下创建时间戳子目录，包含：
- `constellation_charts/` - 星座可见性甘特图
- `data/` - 数据文件
- `logs/` - 日志文件
- `reports/` - 报告文件

## 版本信息

- **版本**: v1.0
- **发布日期**: 2025-07-20
- **状态**: 生产就绪
- **兼容性**: STK 12+, Python 3.8+

## 技术支持

本系统基于以下技术栈：
- STK (Systems Tool Kit)
- Google ADK (Agent Development Kit)
- Python异步编程
- matplotlib可视化

## 许可证

本软件仅供学习和研究使用。
