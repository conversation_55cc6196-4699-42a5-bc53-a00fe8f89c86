"""
多目标元任务分解可视化器
严格按照项目成功经验和标准图片样式实现多目标元任务分解甘特图
图片样式特征：
- 时间轴：st_m → end_n
- 任务条：灰色填充，黑色边框，白色间隔
- 原子任务序号：底部圆圈编号 1-20，最后δt
- 垂直分割线：黑色实线分割原子任务
- 时间标记：st1, st2, st3, st4 和 end1, end2, end3, end4
"""

import matplotlib.pyplot as plt
import matplotlib.patches as patches
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import logging
import os

logger = logging.getLogger(__name__)

class MultiTargetMetaTaskVisualizer:
    """多目标元任务分解可视化器 - 严格按照标准图片样式"""
    
    def __init__(self, output_manager=None):
        """初始化可视化器"""
        self.fig = None
        self.ax = None
        self.output_manager = output_manager
        
        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False
        
        # 严格按照图片样式的颜色配置
        self.colors = {
            'task_fill': '#808080',      # 灰色填充 - 按照图片样式
            'task_border': '#000000',    # 黑色边框 - 按照图片样式
            'white_fill': '#FFFFFF',     # 白色填充 - 按照图片样式
            'time_marker': '#000000',    # 黑色时间标记线 - 按照图片样式
            'text': '#000000',           # 黑色文本 - 按照图片样式
            'grid': '#000000',           # 黑色网格线 - 按照图片样式
            'atomic_task': '#FFFFFF'     # 原子任务圆圈填充
        }
    
    def create_multi_target_meta_task_chart(self, targets_task_info: Dict[str, Dict[str, Any]], 
                                           save_path: Optional[str] = None) -> str:
        """
        创建多目标元任务分解甘特图 - 严格按照标准图片样式
        
        Args:
            targets_task_info: 多个目标的任务信息 {target_id: original_task_info}
            save_path: 保存路径（可选）
            
        Returns:
            str: 保存的文件路径
        """
        try:
            if not targets_task_info:
                logger.warning("没有目标任务数据可以可视化")
                return ""
            
            logger.info(f"🎨 创建多目标元任务分解甘特图，目标数量: {len(targets_task_info)}")
            
            # 创建图形 - 按照图片比例
            self.fig, self.ax = plt.subplots(figsize=(16, 8))
            
            # 计算全局时间范围和原子任务数量
            global_start_time, global_end_time, max_atomic_tasks = self._calculate_global_parameters(targets_task_info)
            global_duration = (global_end_time - global_start_time).total_seconds()
            
            logger.info(f"   全局时间范围: {global_start_time} - {global_end_time}")
            logger.info(f"   全局持续时间: {global_duration}秒")
            logger.info(f"   最大原子任务数: {max_atomic_tasks}")
            
            # 绘制每个目标的元任务条 - 按照图片样式
            target_labels = []
            y_positions = []
            
            for i, (target_id, task_info) in enumerate(targets_task_info.items()):
                y_pos = len(targets_task_info) - i - 1  # 从上到下排列
                y_positions.append(y_pos)
                target_labels.append(f"Task{i+1}")  # 按照图片样式：Task1, Task2, Task3, Task4
                
                # 绘制该目标的元任务条
                self._draw_target_meta_task_bar(task_info, y_pos, global_start_time, i+1)
            
            # 绘制时间标记 - 按照图片样式
            self._draw_time_markers(targets_task_info, global_start_time, global_duration)
            
            # 绘制原子任务序号 - 按照图片样式
            self._draw_atomic_task_numbers(max_atomic_tasks, global_duration)
            
            # 设置坐标轴 - 按照图片样式
            self._setup_axes(global_duration, y_positions, target_labels)
            
            # 设置标题 - 按照图片样式
            title = f"多目标元任务分解甘特图\n{len(targets_task_info)}个目标的原子任务时间对齐显示"
            self.ax.set_title(title, fontsize=16, fontweight='bold', pad=20)
            
            # 调整布局
            plt.tight_layout()
            
            # 保存图片
            if save_path is None:
                if self.output_manager:
                    save_path = os.path.join(self.output_manager.session_dir, "multi_target_meta_task_decomposition.png")
                else:
                    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                    save_path = f"multi_target_meta_task_{timestamp}.png"
            
            self.fig.savefig(save_path, dpi=300, bbox_inches='tight', facecolor='white')
            logger.info(f"✅ 多目标元任务分解甘特图已保存: {save_path}")
            
            return save_path
            
        except Exception as e:
            logger.error(f"❌ 创建多目标元任务分解甘特图失败: {e}")
            return ""
    
    def _calculate_global_parameters(self, targets_task_info: Dict[str, Dict[str, Any]]) -> tuple:
        """计算全局参数：时间范围和最大原子任务数"""
        try:
            all_start_times = []
            all_end_times = []
            max_atomic_tasks = 0
            
            for task_info in targets_task_info.values():
                tracking_task = task_info.get("tracking_task", {})
                atomic_tasks = tracking_task.get("atomic_tasks", [])

                if tracking_task:
                    start_time = tracking_task.get("start_time")
                    end_time = tracking_task.get("end_time")

                    if start_time is not None and end_time is not None:
                        all_start_times.append(start_time)
                        all_end_times.append(end_time)
                    else:
                        logger.warning(f"跟踪任务时间数据无效: start_time={start_time}, end_time={end_time}")

                if atomic_tasks:
                    max_atomic_tasks = max(max_atomic_tasks, len(atomic_tasks))
            
            if not all_start_times or not all_end_times:
                # 如果没有有效时间，使用当前时间
                now = datetime.now()
                return now, now + timedelta(hours=1), 20
            
            global_start = min(all_start_times)
            global_end = max(all_end_times)
            
            return global_start, global_end, max_atomic_tasks
            
        except Exception as e:
            logger.error(f"计算全局参数失败: {e}")
            now = datetime.now()
            return now, now + timedelta(hours=1), 20
    
    def _draw_target_meta_task_bar(self, task_info: Dict[str, Any], y_pos: float, 
                                  global_start_time: datetime, task_number: int):
        """绘制单个目标的元任务条 - 严格按照图片样式"""
        try:
            tracking_task = task_info.get("tracking_task", {})
            atomic_tasks = tracking_task.get("atomic_tasks", [])

            if not tracking_task:
                logger.warning("跟踪任务信息为空")
                return

            # 检查时间数据
            task_start = tracking_task.get("start_time")
            task_end = tracking_task.get("end_time")

            if task_start is None or task_end is None:
                logger.warning(f"任务时间数据无效: start_time={task_start}, end_time={task_end}")
                return

            if not atomic_tasks:
                logger.warning("原子任务列表为空")
                return
            
            start_offset = (task_start - global_start_time).total_seconds()
            duration = (task_end - task_start).total_seconds()
            
            # 绘制整个任务条 - 灰色填充，黑色边框
            rect = patches.Rectangle(
                (start_offset, y_pos - 0.4), duration, 0.8,
                linewidth=2, edgecolor=self.colors['task_border'],
                facecolor=self.colors['task_fill'], alpha=0.8
            )
            self.ax.add_patch(rect)
            
            # 绘制原子任务分割线 - 黑色实线
            for i, atomic_task in enumerate(atomic_tasks[:-1]):  # 除了最后一个
                atomic_end = atomic_task["end_time"]
                split_offset = (atomic_end - global_start_time).total_seconds()
                
                # 绘制垂直分割线
                self.ax.plot([split_offset, split_offset], [y_pos - 0.4, y_pos + 0.4], 
                           color=self.colors['task_border'], linewidth=1.5, linestyle='-')
            
            # 添加开始和结束时间标记
            self._add_task_time_labels(start_offset, start_offset + duration, y_pos, task_number)
            
        except Exception as e:
            logger.error(f"绘制目标元任务条失败: {e}")
    
    def _add_task_time_labels(self, start_offset: float, end_offset: float, y_pos: float, task_number: int):
        """添加任务时间标签 - 按照图片样式：st1, st2, st3, st4 和 end1, end2, end3, end4"""
        try:
            # 添加开始时间标记
            self.ax.text(start_offset, y_pos - 0.6, f'st{task_number}', 
                        fontsize=10, ha='center', va='top', fontweight='bold', 
                        color=self.colors['text'])
            
            # 添加结束时间标记
            self.ax.text(end_offset, y_pos - 0.6, f'end{task_number}', 
                        fontsize=10, ha='center', va='top', fontweight='bold', 
                        color=self.colors['text'])
            
            # 绘制时间标记的垂直线
            self.ax.plot([start_offset, start_offset], [y_pos - 0.4, y_pos - 0.5], 
                        color=self.colors['time_marker'], linewidth=1, linestyle='--')
            self.ax.plot([end_offset, end_offset], [y_pos - 0.4, y_pos - 0.5], 
                        color=self.colors['time_marker'], linewidth=1, linestyle='--')
            
        except Exception as e:
            logger.error(f"添加任务时间标签失败: {e}")
    
    def _draw_time_markers(self, targets_task_info: Dict[str, Dict[str, Any]], 
                          global_start_time: datetime, global_duration: float):
        """绘制全局时间标记 - 按照图片样式"""
        try:
            # 绘制全局开始时间标记 st_m
            self.ax.text(-global_duration * 0.05, len(targets_task_info) + 0.2, 'st_m', 
                        fontsize=12, ha='right', va='bottom', fontweight='bold', 
                        color=self.colors['text'])
            
            # 绘制全局结束时间标记 end_n
            self.ax.text(global_duration + global_duration * 0.05, len(targets_task_info) + 0.2, 'end_n', 
                        fontsize=12, ha='left', va='bottom', fontweight='bold', 
                        color=self.colors['text'])
            
        except Exception as e:
            logger.error(f"绘制时间标记失败: {e}")
    
    def _draw_atomic_task_numbers(self, max_atomic_tasks: int, global_duration: float):
        """绘制原子任务序号 - 严格按照图片样式"""
        try:
            # 在底部绘制原子任务序号
            y_pos = -1.2
            
            # 绘制"子任务序号"标签
            self.ax.text(-global_duration * 0.08, y_pos, "子任务序号", 
                        fontsize=12, fontweight='bold', 
                        ha='right', va='center', color=self.colors['text'])
            
            # 计算每个原子任务的位置
            task_width = global_duration / max_atomic_tasks
            
            # 绘制原子任务序号圆圈 1-20
            for i in range(max_atomic_tasks):
                task_center = (i + 0.5) * task_width
                
                # 绘制圆圈
                circle = patches.Circle((task_center, y_pos), 
                                      global_duration * 0.015, 
                                      linewidth=1.5, edgecolor=self.colors['task_border'],
                                      facecolor=self.colors['atomic_task'])
                self.ax.add_patch(circle)
                
                # 添加序号
                self.ax.text(task_center, y_pos, str(i + 1), 
                           fontsize=10, ha='center', va='center', fontweight='bold',
                           color=self.colors['text'])
            
            # 添加δt标记 - 按照图片样式
            delta_t_pos = global_duration + global_duration * 0.03
            circle = patches.Circle((delta_t_pos, y_pos), 
                                  global_duration * 0.015, 
                                  linewidth=1.5, edgecolor=self.colors['task_border'],
                                  facecolor=self.colors['atomic_task'])
            self.ax.add_patch(circle)
            self.ax.text(delta_t_pos, y_pos, 'δt', 
                        fontsize=10, ha='center', va='center', fontweight='bold',
                        color=self.colors['text'])
                
        except Exception as e:
            logger.error(f"绘制原子任务序号失败: {e}")
    
    def _setup_axes(self, global_duration: float, y_positions: List[float], target_labels: List[str]):
        """设置坐标轴 - 按照图片样式"""
        try:
            # 设置X轴
            self.ax.set_xlim(-global_duration * 0.12, global_duration * 1.15)
            self.ax.set_xlabel("时间轴 st_m → end_n", fontsize=12, fontweight='bold')
            
            # 设置Y轴
            self.ax.set_ylim(-1.8, len(target_labels) + 0.5)
            self.ax.set_ylabel("", fontsize=12, fontweight='bold')
            self.ax.set_yticks(y_positions)
            self.ax.set_yticklabels(target_labels, fontsize=11, fontweight='bold')
            
            # 添加网格 - 按照图片样式
            self.ax.grid(True, alpha=0.5, color=self.colors['grid'], linewidth=0.5)
            
            # 设置时间刻度 - 简化显示
            time_ticks = np.linspace(0, global_duration, 6)
            self.ax.set_xticks(time_ticks)
            self.ax.set_xticklabels([f"{int(t)}s" for t in time_ticks], fontsize=10)
            
            # 移除顶部和右侧边框
            self.ax.spines['top'].set_visible(False)
            self.ax.spines['right'].set_visible(False)
            
        except Exception as e:
            logger.error(f"设置坐标轴失败: {e}")
    
    def close(self):
        """关闭图形"""
        if self.fig:
            plt.close(self.fig)
            self.fig = None
            self.ax = None
