#!/usr/bin/env python3
"""
统一配置管理器 - ICBM预警系统
负责管理系统的所有配置参数，确保配置的一致性和可维护性
"""

import os
import yaml
import logging
from typing import Dict, Any, Optional, List
from datetime import datetime
import copy

logger = logging.getLogger(__name__)


class ConfigManager:
    """
    统一配置管理器

    功能：
    1. 从唯一配置文件加载所有配置
    2. 提供配置验证
    3. 支持配置热重载
    4. 提供配置访问的统一接口
    """
    
    _instance = None
    _config = None
    _config_file_path = None
    _last_modified = None
    
    def __new__(cls, config_file_path: str = None):
        """单例模式，确保全局唯一的配置管理器"""
        if cls._instance is None:
            cls._instance = super(ConfigManager, cls).__new__(cls)
        return cls._instance
    
    def __init__(self, config_file_path: str = None):
        """
        初始化配置管理器

        Args:
            config_file_path: 配置文件路径
        """
        if hasattr(self, '_initialized') and self._initialized:
            return
            
        self._initialized = True
        
        # 设置配置文件路径
        if config_file_path is None:
            raise ValueError("必须提供配置文件路径")
        
        self._config_file_path = config_file_path
        self._config = {}
        self._last_modified = None
        
        # 加载配置
        self.load_config()
        
        logger.info(f"🔧 统一配置管理器初始化完成")
        logger.info(f"   配置文件: {self._config_file_path}")
        logger.info(f"   配置项数量: {len(self._config)}")
    
    def load_config(self) -> bool:
        """
        加载配置文件
        
        Returns:
            bool: 加载是否成功
        """
        try:
            if not os.path.exists(self._config_file_path):
                logger.error(f"❌ 配置文件不存在: {self._config_file_path}")
                return False
            
            # 检查文件修改时间
            current_modified = os.path.getmtime(self._config_file_path)
            if self._last_modified and current_modified == self._last_modified:
                logger.debug("配置文件未修改，跳过重新加载")
                return True
            
            # 加载配置文件
            with open(self._config_file_path, 'r', encoding='utf-8') as f:
                self._config = yaml.safe_load(f)
            
            self._last_modified = current_modified
            
            # 验证配置
            if not self._validate_config():
                logger.error("❌ 配置文件验证失败")
                return False
            
            logger.info(f"✅ 配置文件加载成功: {self._config_file_path}")
            return True
            
        except Exception as e:
            logger.error(f"❌ 配置文件加载失败: {e}")
            return False
    
    def _validate_config(self) -> bool:
        """
        验证配置文件的完整性和正确性
        
        Returns:
            bool: 验证是否通过
        """
        try:
            required_sections = [
                'constellation',
                'payload', 
                'missiles',
                'simulation',
                'stk',
                'adk'
            ]
            
            for section in required_sections:
                if section not in self._config:
                    logger.error(f"❌ 缺少必需的配置节: {section}")
                    return False
            
            # 验证星座配置
            constellation = self._config.get('constellation', {})
            required_constellation_fields = ['name', 'type', 'planes', 'satellites_per_plane']
            for field in required_constellation_fields:
                if field not in constellation:
                    logger.error(f"❌ 星座配置缺少必需字段: {field}")
                    return False
            
            # 验证仿真时间配置
            simulation = self._config.get('simulation', {})
            required_time_fields = ['start_time', 'end_time', 'epoch_time']
            for field in required_time_fields:
                if field not in simulation:
                    logger.error(f"❌ 仿真配置缺少必需字段: {field}")
                    return False
            
            logger.info("✅ 配置文件验证通过")
            return True
            
        except Exception as e:
            logger.error(f"❌ 配置验证失败: {e}")
            return False
    

    
    def get_config(self, section: str = None, key: str = None) -> Any:
        """
        获取配置值

        Args:
            section: 配置节名称，如果为None则返回整个配置
            key: 配置键名称，如果为None则返回整个节

        Returns:
            配置值
        """
        try:
            if section is None:
                return copy.deepcopy(self._config)
            
            if section not in self._config:
                raise KeyError(f"配置节不存在: {section}")

            section_config = self._config[section]

            if key is None:
                return copy.deepcopy(section_config)

            if key not in section_config:
                raise KeyError(f"配置键不存在: {section}.{key}")

            return copy.deepcopy(section_config[key])

        except Exception as e:
            logger.error(f"❌ 获取配置失败: {e}")
            raise
    
    def get_constellation_config(self) -> Dict[str, Any]:
        """获取星座配置"""
        return self.get_config('constellation')
    
    def get_payload_config(self) -> Dict[str, Any]:
        """获取载荷配置"""
        return self.get_config('payload')

    def get_missiles_config(self) -> Dict[str, Any]:
        """获取导弹配置"""
        return self.get_config('missiles')

    def get_simulation_config(self) -> Dict[str, Any]:
        """获取仿真配置"""
        return self.get_config('simulation')

    def get_stk_config(self) -> Dict[str, Any]:
        """获取STK配置"""
        return self.get_config('stk')

    def get_adk_config(self) -> Dict[str, Any]:
        """获取ADK配置"""
        return self.get_config('adk')

    def get_output_config(self) -> Dict[str, Any]:
        """获取输出配置"""
        return self.get_config('output')

    def get_task_planning_config(self) -> Dict[str, Any]:
        """获取任务规划配置"""
        return self.get_config('task_planning')
    
    def reload_config(self) -> bool:
        """
        重新加载配置文件
        
        Returns:
            bool: 重载是否成功
        """
        logger.info("🔄 重新加载配置文件...")
        return self.load_config()
    
    def get_config_summary(self) -> Dict[str, Any]:
        """
        获取配置摘要信息
        
        Returns:
            配置摘要
        """
        try:
            constellation = self.get_constellation_config()
            simulation = self.get_simulation_config()
            missiles = self.get_missiles_config()
            
            summary = {
                'config_file': self._config_file_path,
                'last_modified': datetime.fromtimestamp(self._last_modified).isoformat() if self._last_modified else None,
                'constellation': {
                    'name': constellation.get('name', 'Unknown'),
                    'type': constellation.get('type', 'Unknown'),
                    'total_satellites': constellation.get('total_satellites', 0),
                    'planes': constellation.get('planes', 0),
                    'satellites_per_plane': constellation.get('satellites_per_plane', 0)
                },
                'simulation': {
                    'start_time': simulation.get('start_time', 'Unknown'),
                    'end_time': simulation.get('end_time', 'Unknown'),
                    'time_step': simulation.get('time_step', 60)
                },
                'missiles': {
                    'total_threats': len(missiles.get('icbm_threats', []))
                }
            }
            
            return summary
            
        except Exception as e:
            logger.error(f"❌ 获取配置摘要失败: {e}")
            return {}


# 全局配置管理器实例
_global_config_manager = None


def get_config_manager(config_file_path: str = None) -> ConfigManager:
    """
    获取全局配置管理器实例
    
    Args:
        config_file_path: 配置文件路径
        
    Returns:
        ConfigManager: 配置管理器实例
    """
    global _global_config_manager
    
    if _global_config_manager is None:
        _global_config_manager = ConfigManager(config_file_path)
    
    return _global_config_manager


def get_config(section: str = None, key: str = None) -> Any:
    """
    便捷函数：获取配置值

    Args:
        section: 配置节名称
        key: 配置键名称

    Returns:
        配置值
    """
    config_manager = get_config_manager()
    return config_manager.get_config(section, key)
