"""
🕐 统一时间管理器 V2.0 - 集成优化版本

这个模块提供统一的时间管理功能，确保：
1. 所有时间配置从统一配置管理器读取
2. 时间格式转换统一处理
3. 时间偏移计算统一管理
4. STK时间与系统时间同步
5. 导弹发射时间序列管理
6. 可见性计算时间窗口管理
"""

import logging
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, Tuple, List
import re

logger = logging.getLogger(__name__)


class UnifiedTimeManager:
    """
    统一时间管理器 V2.0

    功能：
    1. 统一时间配置管理
    2. STK时间格式转换
    3. 导弹发射时间序列计算
    4. 可见性计算时间窗口管理
    5. 时间同步和偏移管理
    """

    _instance = None

    def __new__(cls, config_manager=None):
        """单例模式，确保全局唯一的时间管理器"""
        if cls._instance is None:
            cls._instance = super(UnifiedTimeManager, cls).__new__(cls)
        return cls._instance

    def __init__(self, config_manager=None):
        """
        初始化统一时间管理器

        Args:
            config_manager: 配置管理器实例
        """
        if hasattr(self, '_initialized') and self._initialized:
            return

        self._initialized = True

        # 导入配置管理器
        if config_manager is None:
            from .config_manager import get_config_manager
            self.config_manager = get_config_manager()
        else:
            self.config_manager = config_manager

        # 获取仿真配置
        self.simulation_config = self.config_manager.get_simulation_config()

        # 解析基准时间
        self._parse_base_times()

        # 初始化时间配置
        self._init_time_configs()

        # 初始化导弹发射时间序列
        self._init_missile_launch_schedule()

        logger.info("🕐 统一时间管理器V2.0初始化完成")
        logger.info(f"   仿真时间范围: {self.start_time_str} - {self.end_time_str}")
        logger.info(f"   历元时间: {self.epoch_time_str}")
        logger.info(f"   导弹发射序列: {len(self.missile_launch_times)}个时间点")
    
    def _parse_base_times(self):
        """解析基准时间"""
        try:
            # 从配置管理器读取基准时间 - 完全基于配置文件，不依赖系统时间
            self.start_time_str = self.simulation_config.get('start_time', '21 Jul 2025 04:00:00.000')
            self.end_time_str = self.simulation_config.get('end_time', '22 Jul 2025 04:00:00.000')
            self.epoch_time_str = self.simulation_config.get('epoch_time', self.start_time_str)

            # 转换为datetime对象
            self.start_time = self._parse_stk_time(self.start_time_str)
            self.end_time = self._parse_stk_time(self.end_time_str)
            self.epoch_time = self._parse_stk_time(self.epoch_time_str)

            if not all([self.start_time, self.end_time, self.epoch_time]):
                raise ValueError("无法解析基准时间")

            # 计算仿真持续时间
            self.simulation_duration = (self.end_time - self.start_time).total_seconds()

            logger.info(f"✅ 基准时间解析成功")
            logger.info(f"   开始时间: {self.start_time_str}")
            logger.info(f"   结束时间: {self.end_time_str}")
            logger.info(f"   仿真持续时间: {self.simulation_duration:.0f}秒")

        except Exception as e:
            logger.error(f"❌ 解析基准时间失败: {e}")
            # 使用默认时间
            self._set_default_times()
    
    def _set_default_times(self):
        """设置默认时间 - 完全基于仿真配置，不依赖系统时间"""
        logger.warning("使用默认时间配置 - 基于仿真时间设置")
        self.start_time_str = "21 Jul 2025 04:00:00.000"
        self.end_time_str = "22 Jul 2025 04:00:00.000"
        self.epoch_time_str = "21 Jul 2025 04:00:00.000"

        self.start_time = datetime(2025, 7, 21, 4, 0, 0)
        self.end_time = datetime(2025, 7, 22, 4, 0, 0)
        self.epoch_time = datetime(2025, 7, 21, 4, 0, 0)
    
    def _init_time_configs(self):
        """初始化时间配置"""
        # 导弹发射时间配置
        missile_schedule = self.simulation_config.get('missile_launch_schedule', {})
        self.base_launch_offset = missile_schedule.get('base_offset', 120)
        self.launch_interval = missile_schedule.get('interval', 60)
        self.max_launch_offset = missile_schedule.get('max_offset', 600)
        
        # 任务时间窗口配置
        task_timing = self.simulation_config.get('task_timing', {})
        self.midcourse_start_offset = task_timing.get('midcourse_start_offset', 300)
        self.midcourse_end_offset = task_timing.get('midcourse_end_offset', 300)
        self.min_midcourse_duration = task_timing.get('min_midcourse_duration', 600)
        self.atomic_task_duration = task_timing.get('atomic_task_duration', 60)
        
        # 可见性计算时间配置
        visibility_timing = self.simulation_config.get('visibility_timing', {})
        self.visibility_start_offset = visibility_timing.get('calculation_start_offset', 0)
        self.visibility_duration = visibility_timing.get('calculation_duration', 86400)
        self.time_resolution = visibility_timing.get('time_resolution', 60)
        
        # 时间管理设置
        self.force_time_reset = self.simulation_config.get('force_time_reset', True)
        self.auto_start_simulation = self.simulation_config.get('auto_start_simulation', False)
        self.time_step = self.simulation_config.get('time_step', 60)
        self.time_zone = self.simulation_config.get('time_zone', 'UTC')

        logger.info(f"✅ 时间配置初始化完成")
        logger.info(f"   时间步长: {self.time_step}秒")
        logger.info(f"   原子任务时长: {self.atomic_task_duration}秒")

    def _init_missile_launch_schedule(self):
        """初始化导弹发射时间序列"""
        try:
            # 获取导弹配置
            missiles_config = self.config_manager.get_missiles_config()
            icbm_threats = missiles_config.get('icbm_threats', [])

            self.missile_launch_times = {}

            for threat in icbm_threats:
                missile_id = threat.get('missile_id', '')
                launch_sequence = threat.get('launch_sequence', 1)

                # 计算发射时间偏移
                launch_offset = self.base_launch_offset + (launch_sequence - 1) * self.launch_interval

                # 确保不超过最大偏移
                if launch_offset > self.max_launch_offset:
                    launch_offset = self.max_launch_offset

                # 计算实际发射时间
                launch_time = self.start_time + timedelta(seconds=launch_offset)
                launch_time_str = self._format_stk_time(launch_time)

                self.missile_launch_times[missile_id] = {
                    'launch_sequence': launch_sequence,
                    'launch_offset': launch_offset,
                    'launch_time': launch_time,
                    'launch_time_str': launch_time_str,
                    'description': threat.get('description', '')
                }

                logger.info(f"   导弹 {missile_id}: 序号{launch_sequence}, 偏移{launch_offset}s, 时间{launch_time_str}")

            logger.info(f"✅ 导弹发射时间序列初始化完成: {len(self.missile_launch_times)}个导弹")

        except Exception as e:
            logger.error(f"❌ 导弹发射时间序列初始化失败: {e}")
            self.missile_launch_times = {}
    
    def get_stk_time_range(self) -> Tuple[str, str, str]:
        """
        获取STK格式的时间范围

        Returns:
            (start_time, end_time, epoch_time) STK格式时间字符串
        """
        return self.start_time_str, self.end_time_str, self.epoch_time_str

    def get_missile_launch_time(self, missile_id: str) -> Optional[Dict[str, Any]]:
        """
        获取指定导弹的发射时间信息

        Args:
            missile_id: 导弹ID

        Returns:
            导弹发射时间信息字典，如果不存在则返回None
        """
        return self.missile_launch_times.get(missile_id)

    def get_all_missile_launch_times(self) -> Dict[str, Dict[str, Any]]:
        """
        获取所有导弹的发射时间信息

        Returns:
            所有导弹发射时间信息字典
        """
        return self.missile_launch_times.copy()

    def calculate_midcourse_window(self, missile_id: str) -> Optional[Tuple[datetime, datetime]]:
        """
        计算导弹中段飞行时间窗口

        Args:
            missile_id: 导弹ID

        Returns:
            (start_time, end_time) 中段飞行时间窗口，如果计算失败则返回None
        """
        try:
            missile_info = self.get_missile_launch_time(missile_id)
            if not missile_info:
                logger.warning(f"⚠️ 未找到导弹 {missile_id} 的发射时间信息")
                return None

            launch_time = missile_info['launch_time']

            # 计算中段飞行开始和结束时间
            midcourse_start = launch_time + timedelta(seconds=self.midcourse_start_offset)
            midcourse_end = launch_time + timedelta(seconds=self.midcourse_end_offset)

            # 确保最小持续时间
            if (midcourse_end - midcourse_start).total_seconds() < self.min_midcourse_duration:
                midcourse_end = midcourse_start + timedelta(seconds=self.min_midcourse_duration)

            return midcourse_start, midcourse_end

        except Exception as e:
            logger.error(f"❌ 计算导弹 {missile_id} 中段飞行窗口失败: {e}")
            return None

    def generate_atomic_tasks_timeline(self, start_time: datetime, end_time: datetime) -> List[Tuple[datetime, datetime]]:
        """
        生成原子任务时间线

        Args:
            start_time: 开始时间
            end_time: 结束时间

        Returns:
            原子任务时间段列表
        """
        try:
            tasks = []
            current_time = start_time

            while current_time < end_time:
                task_end = current_time + timedelta(seconds=self.atomic_task_duration)
                if task_end > end_time:
                    task_end = end_time

                tasks.append((current_time, task_end))
                current_time = task_end

            logger.info(f"✅ 生成原子任务时间线: {len(tasks)}个任务")
            return tasks

        except Exception as e:
            logger.error(f"❌ 生成原子任务时间线失败: {e}")
            return []

    def is_time_in_simulation_range(self, check_time: datetime) -> bool:
        """
        检查时间是否在仿真范围内

        Args:
            check_time: 要检查的时间

        Returns:
            是否在仿真范围内
        """
        return self.start_time <= check_time <= self.end_time

    def convert_to_stk_time(self, dt: datetime) -> str:
        """将datetime对象转换为STK时间格式"""
        try:
            return dt.strftime("%d %b %Y %H:%M:%S.%f")[:-3]  # 去掉最后3位微秒
        except Exception as e:
            logger.error(f"转换STK时间失败: {e}")
            return self.start_time_str

    def get_time_offset_from_start(self, target_time: datetime) -> float:
        """
        计算相对于仿真开始时间的偏移

        Args:
            target_time: 目标时间

        Returns:
            偏移秒数
        """
        return (target_time - self.start_time).total_seconds()

    def get_simulation_progress(self, current_time: datetime) -> float:
        """
        计算仿真进度百分比

        Args:
            current_time: 当前时间

        Returns:
            进度百分比 (0.0-1.0)
        """
        if current_time <= self.start_time:
            return 0.0
        elif current_time >= self.end_time:
            return 1.0
        else:
            elapsed = (current_time - self.start_time).total_seconds()
            return elapsed / self.simulation_duration
    
    def get_datetime_range(self) -> Tuple[datetime, datetime, datetime]:
        """
        获取datetime格式的时间范围
        
        Returns:
            (start_time, end_time, epoch_time) datetime对象
        """
        return self.start_time, self.end_time, self.epoch_time
    
    def calculate_missile_launch_time(self, launch_sequence: int) -> Tuple[datetime, str]:
        """
        计算导弹发射时间
        
        Args:
            launch_sequence: 发射序号 (1, 2, 3, ...)
            
        Returns:
            (launch_time_dt, launch_time_stk) datetime对象和STK格式字符串
        """
        try:
            # 计算发射偏移时间
            launch_offset = self.base_launch_offset + (launch_sequence - 1) * self.launch_interval
            
            # 确保不超过最大偏移时间
            launch_offset = min(launch_offset, self.max_launch_offset)
            
            # 计算发射时间
            launch_time_dt = self.start_time + timedelta(seconds=launch_offset)
            
            # 确保发射时间在仿真范围内
            if launch_time_dt >= self.end_time:
                # 如果超出范围，调整到仿真开始后1分钟
                launch_time_dt = self.start_time + timedelta(minutes=1)
                logger.warning(f"导弹{launch_sequence}发射时间调整到仿真开始后1分钟")
            
            # 转换为STK格式
            launch_time_stk = self._format_stk_time(launch_time_dt)
            
            logger.info(f"导弹{launch_sequence}发射时间: {launch_time_stk}")
            return launch_time_dt, launch_time_stk
            
        except Exception as e:
            logger.error(f"计算导弹发射时间失败: {e}")
            # 返回默认时间
            default_time = self.start_time + timedelta(minutes=launch_sequence)
            return default_time, self._format_stk_time(default_time)
    
    def calculate_midcourse_time_window(self, launch_time: datetime, 
                                      flight_duration: float) -> Tuple[datetime, datetime]:
        """
        计算中段飞行时间窗口
        
        Args:
            launch_time: 发射时间
            flight_duration: 飞行持续时间(秒)
            
        Returns:
            (midcourse_start, midcourse_end) 中段飞行时间窗口
        """
        try:
            # 计算撞击时间
            impact_time = launch_time + timedelta(seconds=flight_duration)
            
            # 计算中段飞行时间窗口
            midcourse_start = launch_time + timedelta(seconds=self.midcourse_start_offset)
            midcourse_end = impact_time - timedelta(seconds=self.midcourse_end_offset)
            
            # 确保中段飞行时间足够长
            midcourse_duration = (midcourse_end - midcourse_start).total_seconds()
            if midcourse_duration < self.min_midcourse_duration:
                # 调整中段飞行时间
                total_adjustment = self.min_midcourse_duration - midcourse_duration
                midcourse_start = midcourse_start - timedelta(seconds=total_adjustment/2)
                midcourse_end = midcourse_end + timedelta(seconds=total_adjustment/2)
                
                logger.info(f"中段飞行时间调整为最小持续时间: {self.min_midcourse_duration}秒")
            
            return midcourse_start, midcourse_end
            
        except Exception as e:
            logger.error(f"计算中段飞行时间窗口失败: {e}")
            # 返回默认窗口
            default_start = launch_time + timedelta(minutes=5)
            default_end = default_start + timedelta(seconds=self.min_midcourse_duration)
            return default_start, default_end
    
    def calculate_visibility_time_window(self) -> Tuple[datetime, datetime]:
        """
        计算可见性分析时间窗口
        
        Returns:
            (visibility_start, visibility_end) 可见性分析时间窗口
        """
        try:
            visibility_start = self.start_time + timedelta(seconds=self.visibility_start_offset)
            visibility_end = visibility_start + timedelta(seconds=self.visibility_duration)
            
            # 确保不超过仿真结束时间
            if visibility_end > self.end_time:
                visibility_end = self.end_time
                logger.warning("可见性分析时间窗口调整到仿真结束时间")
            
            return visibility_start, visibility_end
            
        except Exception as e:
            logger.error(f"计算可见性时间窗口失败: {e}")
            return self.start_time, self.end_time
    
    def _parse_stk_time(self, stk_time_str: str) -> Optional[datetime]:
        """解析STK时间字符串为datetime对象"""
        try:
            # STK时间格式: "21 Jul 2025 04:00:00.000"
            return datetime.strptime(stk_time_str, "%d %b %Y %H:%M:%S.%f")
        except:
            try:
                # 尝试不带毫秒的格式
                return datetime.strptime(stk_time_str, "%d %b %Y %H:%M:%S")
            except:
                logger.warning(f"无法解析STK时间格式: {stk_time_str}")
                return None
    
    def _format_stk_time(self, dt: datetime) -> str:
        """将datetime对象格式化为STK时间字符串"""
        try:
            return dt.strftime("%d %b %Y %H:%M:%S.%f")[:-3]
        except:
            return dt.strftime("%d %b %Y %H:%M:%S.000")
    
    def add_time_offset(self, base_time: datetime, offset_seconds: float) -> datetime:
        """
        为基准时间添加偏移
        
        Args:
            base_time: 基准时间
            offset_seconds: 偏移秒数
            
        Returns:
            调整后的时间
        """
        return base_time + timedelta(seconds=offset_seconds)
    
    def get_time_info_summary(self) -> Dict[str, Any]:
        """
        获取时间配置摘要信息
        
        Returns:
            时间配置摘要
        """
        return {
            "simulation_time_range": {
                "start": self.start_time_str,
                "end": self.end_time_str,
                "epoch": self.epoch_time_str,
                "duration_hours": (self.end_time - self.start_time).total_seconds() / 3600
            },
            "missile_launch_config": {
                "base_offset": self.base_launch_offset,
                "interval": self.launch_interval,
                "max_offset": self.max_launch_offset
            },
            "task_timing_config": {
                "midcourse_start_offset": self.midcourse_start_offset,
                "midcourse_end_offset": self.midcourse_end_offset,
                "min_midcourse_duration": self.min_midcourse_duration,
                "atomic_task_duration": self.atomic_task_duration
            },
            "visibility_config": {
                "start_offset": self.visibility_start_offset,
                "duration": self.visibility_duration,
                "resolution": self.time_resolution
            }
        }


# 全局时间管理器实例
_global_time_manager = None


def get_time_manager(config_manager=None) -> UnifiedTimeManager:
    """
    获取全局时间管理器实例

    Args:
        config_manager: 配置管理器实例

    Returns:
        UnifiedTimeManager: 时间管理器实例
    """
    global _global_time_manager

    if _global_time_manager is None:
        _global_time_manager = UnifiedTimeManager(config_manager)

    return _global_time_manager


def get_stk_time_range() -> Tuple[str, str, str]:
    """
    便捷函数：获取STK格式的时间范围

    Returns:
        (start_time, end_time, epoch_time) STK格式时间字符串
    """
    time_manager = get_time_manager()
    return time_manager.get_stk_time_range()


def get_missile_launch_time(missile_id: str) -> Optional[Dict[str, Any]]:
    """
    便捷函数：获取指定导弹的发射时间信息

    Args:
        missile_id: 导弹ID

    Returns:
        导弹发射时间信息字典
    """
    time_manager = get_time_manager()
    return time_manager.get_missile_launch_time(missile_id)


def calculate_midcourse_window(missile_id: str) -> Optional[Tuple[datetime, datetime]]:
    """
    便捷函数：计算导弹中段飞行时间窗口

    Args:
        missile_id: 导弹ID

    Returns:
        (start_time, end_time) 中段飞行时间窗口
    """
    time_manager = get_time_manager()
    return time_manager.calculate_midcourse_window(missile_id)


def format_stk_time(dt: datetime) -> str:
    """
    便捷函数：格式化为STK时间字符串

    Args:
        dt: datetime对象

    Returns:
        STK格式时间字符串
    """
    time_manager = get_time_manager()
    return time_manager._format_stk_time(dt)


def parse_stk_time(time_str: str) -> Optional[datetime]:
    """
    便捷函数：解析STK时间字符串

    Args:
        time_str: STK格式时间字符串

    Returns:
        datetime对象
    """
    time_manager = get_time_manager()
    return time_manager._parse_stk_time(time_str)
