"""
ADK框架模块 V2.0 - 优化后的多智能体框架
只保留实际使用的最优组件
"""

# 导入核心ADK组件
try:
    from .real_adk_agent import RealADKSatelliteAgent
    from .enhanced_adk_agent import EnhancedADKSatelliteAgent
    from .satellite_agent_mapper import SatelliteAgentMapper
    from .distributed_agent_manager import DistributedAgentManager
    from .meta_task_coordinator import MetaTaskCoordinator

    __all__ = [
        'RealADKSatelliteAgent',
        'EnhancedADKSatelliteAgent',
        'SatelliteAgentMapper',
        'DistributedAgentManager',
        'MetaTaskCoordinator'
    ]

    print("✅ ADK框架模块V2.0导入成功 - 优化后的组件")

except ImportError as e:
    print(f"❌ ADK框架模块导入失败: {e}")
    raise ImportError("ADK框架模块无法导入，系统无法运行")
