#!/usr/bin/env python3
"""
输出管理器模块
负责统一管理系统输出文件和目录
"""

import os
import uuid
import random
import logging
from datetime import datetime
from typing import Optional

logger = logging.getLogger(__name__)

class OutputManager:
    """输出管理器"""
    
    def __init__(self, base_dir: str = "test_outputs"):
        """初始化输出管理器"""
        self.base_dir = base_dir

        # 生成唯一的会话标识符 - 每次运行都不同
        # 方法1: 使用UUID的一部分
        unique_id = str(uuid.uuid4())[:8]

        # 方法2: 使用当前系统时间（确保唯一性）
        from datetime import datetime
        current_time = datetime.now()
        time_str = current_time.strftime("%Y%m%d_%H%M%S_%f")[:17]  # 包含微秒前3位

        # 方法3: 使用随机数和进程ID
        process_id = os.getpid()
        random_num = random.randint(1000, 9999)

        # 组合生成唯一的会话标识符
        self.session_timestamp = f"sim_{time_str}_{unique_id}_{process_id}_{random_num}"

        # 尝试获取仿真时间作为前缀（可选）
        try:
            from src.utils.time_manager import get_time_manager
            time_manager = get_time_manager()
            sim_time = time_manager.start_time
            sim_prefix = sim_time.strftime("%Y%m%d_%H%M%S")
            self.session_timestamp = f"{sim_prefix}_{unique_id}_{random_num}"
        except:
            # 如果时间管理器不可用，使用完整的唯一标识符
            pass

        self.session_dir = os.path.join(self.base_dir, self.session_timestamp)
        
        # 创建输出目录结构
        self._create_directory_structure()
        
        logger.info(f"📁 输出管理器初始化，会话目录: {self.session_dir}")
    
    def _create_directory_structure(self):
        """创建目录结构"""
        try:
            # 主目录
            os.makedirs(self.session_dir, exist_ok=True)
            
            # 子目录
            subdirs = [
                "constellation_charts",
                "gantt_charts", 
                "visibility_charts",
                "data",
                "logs",
                "reports",
                "scenarios"
            ]
            
            for subdir in subdirs:
                subdir_path = os.path.join(self.session_dir, subdir)
                os.makedirs(subdir_path, exist_ok=True)
            
            logger.info(f"✅ 目录结构创建成功: {len(subdirs)} 个子目录")
            
        except Exception as e:
            logger.error(f"❌ 目录结构创建失败: {e}")
    
    def save_constellation_chart(self, filename: str) -> str:
        """保存星座图表"""
        try:
            chart_dir = os.path.join(self.session_dir, "constellation_charts")
            output_path = os.path.join(chart_dir, filename)
            
            logger.info(f"📊 星座图表保存路径: {output_path}")
            return output_path
            
        except Exception as e:
            logger.error(f"❌ 星座图表路径生成失败: {e}")
            return filename
    
    def save_gantt_chart(self, filename: str) -> str:
        """保存甘特图"""
        try:
            gantt_dir = os.path.join(self.session_dir, "gantt_charts")
            os.makedirs(gantt_dir, exist_ok=True)  # 确保目录存在
            output_path = os.path.join(gantt_dir, filename)

            logger.info(f"📈 甘特图保存路径: {output_path}")
            return output_path

        except Exception as e:
            logger.error(f"❌ 甘特图路径生成失败: {e}")
            return filename
    
    def save_visibility_chart(self, filename: str) -> str:
        """保存可见性图表"""
        try:
            visibility_dir = os.path.join(self.session_dir, "visibility_charts")
            os.makedirs(visibility_dir, exist_ok=True)  # 确保目录存在
            output_path = os.path.join(visibility_dir, filename)

            logger.info(f"👁️  可见性图表保存路径: {output_path}")
            return output_path

        except Exception as e:
            logger.error(f"❌ 可见性图表路径生成失败: {e}")
            return filename
    
    def save_data_file(self, filename: str) -> str:
        """保存数据文件"""
        try:
            data_dir = os.path.join(self.session_dir, "data")
            output_path = os.path.join(data_dir, filename)
            
            logger.info(f"💾 数据文件保存路径: {output_path}")
            return output_path
            
        except Exception as e:
            logger.error(f"❌ 数据文件路径生成失败: {e}")
            return filename
    
    def save_report(self, filename: str) -> str:
        """保存报告文件"""
        try:
            report_dir = os.path.join(self.session_dir, "reports")
            output_path = os.path.join(report_dir, filename)
            
            logger.info(f"📋 报告文件保存路径: {output_path}")
            return output_path
            
        except Exception as e:
            logger.error(f"❌ 报告文件路径生成失败: {e}")
            return filename
    
    def get_session_info(self) -> dict:
        """获取会话信息"""
        try:
            session_info = {
                "session_timestamp": self.session_timestamp,
                "session_dir": self.session_dir,
                "base_dir": self.base_dir,
                "created_time": datetime.now().isoformat()
            }
            
            return session_info
            
        except Exception as e:
            logger.error(f"❌ 获取会话信息失败: {e}")
            return {}
