"""
导弹管理器 - 清理版本
负责管理STK场景中的导弹对象，包括创建、配置和轨迹计算
"""

import logging
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta
import math

logger = logging.getLogger(__name__)

class MissileManager:
    """导弹管理器 - 重新设计的导弹对象状态管理"""
    
    def __init__(self, stk_manager, config: Dict[str, Any], time_manager):
        """初始化导弹管理器"""
        self.stk_manager = stk_manager
        self.config = config
        self.time_manager = time_manager
        self.missile_targets = {}
        
    def add_missile_target(self, missile_id: str, launch_position: Dict[str, float], 
                          target_position: Dict[str, float], launch_sequence: int = 1):
        """添加导弹目标配置"""
        self.missile_targets[missile_id] = {
            "launch_position": launch_position,
            "target_position": target_position,
            "launch_sequence": launch_sequence,
            "flight_time": 1800  # 默认30分钟
        }
        logger.info(f"✅ 添加导弹目标配置: {missile_id}")
        
    def create_missile(self, missile_id: str, launch_time: datetime) -> bool:
        """创建导弹对象"""
        try:
            logger.info(f"🚀 创建导弹对象: {missile_id}")
            
            # 获取导弹配置
            missile_info = self.missile_targets.get(missile_id)
            if not missile_info:
                logger.error(f"❌ 未找到导弹配置: {missile_id}")
                return False
                
            # 准备轨迹参数
            trajectory_params = {
                "launch_position": missile_info["launch_position"],
                "target_position": missile_info["target_position"]
            }
            
            # 创建STK导弹对象
            success = self._create_stk_missile_object(missile_id, launch_time, trajectory_params)
            
            if success:
                logger.info(f"✅ 导弹对象创建成功: {missile_id}")
                return True
            else:
                logger.error(f"❌ 导弹对象创建失败: {missile_id}")
                return False
                
        except Exception as e:
            logger.error(f"❌ 创建导弹失败: {e}")
            return False
            
    def _create_stk_missile_object(self, missile_id: str, launch_time: datetime, 
                                  trajectory_params: Dict[str, Any]) -> bool:
        """创建STK导弹对象并配置轨迹"""
        try:
            logger.info(f"🎯 创建STK导弹对象: {missile_id}")
            
            # 1. 创建导弹对象
            try:
                missile = self.stk_manager.scenario.Children.New(13, missile_id)  # eMissile
                logger.info(f"✅ 导弹对象创建成功: {missile_id}")
            except Exception as create_error:
                logger.error(f"❌ 导弹对象创建失败: {create_error}")
                return False
                
            # 2. 设置轨迹类型为弹道
            try:
                missile.SetTrajectoryType(10)  # ePropagatorBallistic
                logger.info(f"✅ 轨迹类型设置为弹道: {missile.TrajectoryType}")
            except Exception as type_error:
                logger.error(f"❌ 轨迹类型设置失败: {type_error}")
                return False
                
            # 3. 设置导弹时间属性
            try:
                launch_time_stk = self._convert_to_stk_time_format(launch_time)
                impact_time = launch_time + timedelta(seconds=1800)
                impact_time_stk = self._convert_to_stk_time_format(impact_time)
                
                missile.SetTimePeriod(launch_time_stk, impact_time_stk)
                logger.info(f"✅ 导弹时间设置成功: {launch_time_stk} - {impact_time_stk}")
            except Exception as time_error:
                logger.warning(f"⚠️  导弹时间设置失败: {time_error}")
                
            # 4. 配置轨迹参数
            try:
                trajectory = missile.Trajectory
                launch_pos = trajectory_params["launch_position"]
                target_pos = trajectory_params["target_position"]
                
                # 设置发射位置
                trajectory.Launch.Lat = launch_pos["lat"]
                trajectory.Launch.Lon = launch_pos["lon"]
                trajectory.Launch.Alt = launch_pos["alt"]
                logger.info(f"✅ 发射位置设置成功")
                
                # 设置撞击位置
                trajectory.ImpactLocation.Impact.Lat = target_pos["lat"]
                trajectory.ImpactLocation.Impact.Lon = target_pos["lon"]
                trajectory.ImpactLocation.Impact.Alt = target_pos["alt"]
                logger.info(f"✅ 撞击位置设置成功")
                
                # 设置发射控制类型和远地点高度
                range_m = self._calculate_great_circle_distance(launch_pos, target_pos)
                range_km = range_m / 1000.0
                apogee_alt_km = min(max(range_km * 0.3, 300), 1500)
                
                trajectory.ImpactLocation.SetLaunchControlType(0)
                trajectory.impactLocation.LaunchControl.ApogeeAlt = apogee_alt_km
                logger.info(f"✅ 发射控制设置成功: {apogee_alt_km:.1f}km")
                
                # 执行传播
                trajectory.Propagate()
                logger.info(f"✅ 轨迹传播成功")
                
            except Exception as traj_error:
                logger.warning(f"⚠️  轨迹参数设置失败: {traj_error}")
                
            return True
            
        except Exception as e:
            logger.error(f"❌ STK导弹对象创建失败: {e}")
            return False
            
    def get_missile_trajectory_info(self, missile_id: str) -> Optional[Dict[str, Any]]:
        """获取导弹轨迹信息 - 重新设计的状态管理策略"""
        try:
            logger.info(f"🎯 获取导弹轨迹信息: {missile_id}")
            
            # 获取导弹对象
            try:
                missile = self.stk_manager.scenario.Children.Item(missile_id)
                logger.info(f"✅ 导弹对象获取成功: {missile_id}")
            except Exception as missile_error:
                logger.error(f"❌ 导弹对象获取失败: {missile_error}")
                return None
                
            # 使用重新设计的轨迹信息计算方法
            return self._calculate_trajectory_info(missile)
            
        except Exception as e:
            logger.error(f"❌ 获取导弹轨迹信息失败: {e}")
            return None
            
    def _calculate_trajectory_info(self, missile) -> Optional[Dict[str, Any]]:
        """计算轨迹信息 - 重新设计的导弹对象管理策略"""
        try:
            logger.info(f"🎯 开始获取导弹真实轨迹信息...")
            
            missile_id = missile.InstanceName
            
            # 检查导弹对象状态
            logger.info(f"🔍 检查导弹对象状态: {missile_id}")
            object_state = self._analyze_missile_object_state(missile)
            
            if object_state["can_reconfigure"]:
                # 导弹对象可以重新配置
                logger.info(f"🚀 导弹对象可重新配置，尝试获取真实轨迹...")
                trajectory_info = self._reconfigure_missile_for_real_trajectory(missile)
                
                if trajectory_info:
                    logger.info(f"✅ 重新配置成功获取STK真实轨迹数据")
                    return trajectory_info
            else:
                # 导弹对象状态受限，尝试从现有状态提取数据
                logger.info(f"⚠️ 导弹对象状态受限，尝试从现有状态提取数据...")
                trajectory_info = self._extract_trajectory_from_existing_state(missile)
                
                if trajectory_info:
                    logger.info(f"✅ 从现有状态成功提取轨迹数据")
                    return trajectory_info
            
            # 最终回退：重新创建导弹对象
            logger.info(f"🔄 最终回退：重新创建导弹对象...")
            trajectory_info = self._recreate_missile_for_real_trajectory(missile_id)
            
            if trajectory_info:
                logger.info(f"✅ 重新创建成功获取STK真实轨迹数据")
                return trajectory_info
            else:
                logger.error(f"❌ 所有真实轨迹获取方法都失败")
                return None

        except Exception as e:
            logger.error(f"❌ 计算轨迹信息失败: {e}")
            return None
            
    def _analyze_missile_object_state(self, missile) -> Dict[str, Any]:
        """分析导弹对象状态，确定是否可以重新配置"""
        try:
            logger.info(f"   🔍 分析导弹对象状态...")
            
            state_info = {
                "can_reconfigure": False,
                "trajectory_type": None,
                "has_trajectory": False,
                "is_propagated": False,
                "start_time": None,
                "stop_time": None,
                "error_details": []
            }
            
            # 检查轨迹类型
            try:
                trajectory_type = missile.TrajectoryType
                state_info["trajectory_type"] = trajectory_type
                logger.info(f"   📊 轨迹类型: {trajectory_type}")
            except Exception as e:
                state_info["error_details"].append(f"轨迹类型检查失败: {e}")
            
            # 检查是否有轨迹对象
            try:
                trajectory = missile.Trajectory
                state_info["has_trajectory"] = True
                logger.info(f"   ✅ 轨迹对象存在")
                
                # 检查轨迹是否已传播
                try:
                    start_time = trajectory.StartTime
                    state_info["start_time"] = start_time
                    state_info["is_propagated"] = True
                    logger.info(f"   📅 开始时间: {start_time}")
                except Exception as start_time_error:
                    state_info["error_details"].append(f"开始时间访问失败: {start_time_error}")
                    logger.warning(f"   ⚠️  开始时间访问失败: {start_time_error}")
                
                # 检查结束时间
                try:
                    stop_time = trajectory.StopTime
                    state_info["stop_time"] = stop_time
                    logger.info(f"   📅 结束时间: {stop_time}")
                except Exception as stop_time_error:
                    state_info["error_details"].append(f"结束时间访问失败: {stop_time_error}")
                
            except Exception as traj_error:
                state_info["error_details"].append(f"轨迹对象访问失败: {traj_error}")
                logger.warning(f"   ⚠️  轨迹对象访问失败: {traj_error}")
            
            # 判断是否可以重新配置
            if (state_info["trajectory_type"] == 10 and 
                state_info["has_trajectory"] and 
                not state_info["is_propagated"]):
                state_info["can_reconfigure"] = True
                logger.info(f"   ✅ 导弹对象可以重新配置")
            else:
                logger.info(f"   ❌ 导弹对象状态受限，无法重新配置")
            
            return state_info
            
        except Exception as e:
            logger.error(f"   ❌ 导弹对象状态分析失败: {e}")
            return {
                "can_reconfigure": False,
                "error_details": [f"状态分析失败: {e}"]
            }

    def _reconfigure_missile_for_real_trajectory(self, missile) -> Optional[Dict[str, Any]]:
        """重新配置导弹对象以获取真实轨迹"""
        try:
            logger.info(f"   🔧 尝试重新配置导弹对象...")

            missile_id = missile.InstanceName
            missile_info = self.missile_targets.get(missile_id)

            if not missile_info:
                logger.error(f"   ❌ 未找到导弹配置信息: {missile_id}")
                return None

            # 获取发射和目标位置
            launch_pos = missile_info.get("launch_position", {})
            target_pos = missile_info.get("target_position", {})

            if not launch_pos or not target_pos:
                logger.error(f"   ❌ 导弹配置缺少位置信息")
                return None

            # 从导弹名称提取序号
            launch_sequence = 1
            if "Threat_" in missile_id:
                try:
                    sequence_str = missile_id.split("_")[-1]
                    launch_sequence = int(sequence_str)
                except (ValueError, IndexError):
                    pass

            # 使用统一时间管理器计算时间
            launch_time_dt, launch_time_stk = self.time_manager.calculate_missile_launch_time(launch_sequence)

            # 尝试重新配置轨迹
            try:
                trajectory = missile.Trajectory

                # 尝试设置开始时间
                try:
                    trajectory.StartTime = launch_time_stk
                    logger.info(f"   ✅ 开始时间重新设置成功: {launch_time_stk}")
                except Exception as time_error:
                    logger.warning(f"   ⚠️  开始时间重新设置失败: {time_error}")

                # 重新设置发射位置
                trajectory.Launch.Lat = launch_pos["lat"]
                trajectory.Launch.Lon = launch_pos["lon"]
                trajectory.Launch.Alt = launch_pos["alt"] / 1000.0  # 转换为km
                logger.info(f"   ✅ 发射位置重新设置成功")

                # 重新设置撞击位置
                trajectory.ImpactLocation.Impact.Lat = target_pos["lat"]
                trajectory.ImpactLocation.Impact.Lon = target_pos["lon"]
                trajectory.ImpactLocation.Impact.Alt = target_pos["alt"] / 1000.0  # 转换为km
                logger.info(f"   ✅ 撞击位置重新设置成功")

                # 重新设置发射控制类型和远地点高度
                range_m = self._calculate_great_circle_distance(launch_pos, target_pos)
                range_km = range_m / 1000.0
                apogee_alt_km = min(max(range_km * 0.3, 300), 1500)  # 300-1500km范围

                trajectory.ImpactLocation.SetLaunchControlType(0)  # eLaunchControlFixedApogeeAlt
                trajectory.impactLocation.LaunchControl.ApogeeAlt = apogee_alt_km  # 注意小写
                logger.info(f"   ✅ 发射控制重新设置成功: {apogee_alt_km:.1f}km")

                # 重新执行传播
                trajectory.Propagate()
                logger.info(f"   ✅ 轨迹重新传播成功")

                # 提取真实轨迹数据
                return self._extract_real_trajectory_from_configured_missile(missile, launch_time_dt)

            except Exception as reconfig_error:
                logger.error(f"   ❌ 导弹重新配置失败: {reconfig_error}")
                return None

        except Exception as e:
            logger.error(f"❌ 重新配置导弹失败: {e}")
            return None

    def _extract_trajectory_from_existing_state(self, missile) -> Optional[Dict[str, Any]]:
        """从现有导弹状态提取轨迹数据"""
        try:
            logger.info(f"   🔍 从现有导弹状态提取轨迹数据...")

            # 尝试使用DataProvider从现有状态提取数据
            return self._get_stk_dataprovider_trajectory(missile)

        except Exception as e:
            logger.error(f"❌ 从现有状态提取轨迹数据失败: {e}")
            return None

    def _recreate_missile_for_real_trajectory(self, missile_id: str) -> Optional[Dict[str, Any]]:
        """重新创建导弹对象以获取真实轨迹"""
        try:
            logger.info(f"   🔄 重新创建导弹对象: {missile_id}")

            # 删除现有导弹对象
            try:
                self._cleanup_existing_missile(missile_id)
                logger.info(f"   ✅ 现有导弹对象已删除")
            except Exception as cleanup_error:
                logger.warning(f"   ⚠️  导弹对象删除失败: {cleanup_error}")

            # 获取导弹配置信息
            missile_info = self.missile_targets.get(missile_id)
            if not missile_info:
                logger.error(f"   ❌ 未找到导弹配置信息: {missile_id}")
                return None

            # 从导弹名称提取序号
            launch_sequence = 1
            if "Threat_" in missile_id:
                try:
                    sequence_str = missile_id.split("_")[-1]
                    launch_sequence = int(sequence_str)
                except (ValueError, IndexError):
                    pass

            # 使用统一时间管理器计算时间
            launch_time_dt, launch_time_stk = self.time_manager.calculate_missile_launch_time(launch_sequence)

            # 重新创建导弹对象
            trajectory_params = {
                "launch_position": missile_info.get("launch_position", {}),
                "target_position": missile_info.get("target_position", {})
            }

            # 调用原有的创建方法
            success = self._create_stk_missile_object(missile_id, launch_time_dt, trajectory_params)

            if success:
                # 获取新创建的导弹对象
                try:
                    new_missile = self.stk_manager.scenario.Children.Item(missile_id)
                    return self._extract_real_trajectory_from_configured_missile(new_missile, launch_time_dt)
                except Exception as get_error:
                    logger.error(f"   ❌ 获取新创建的导弹对象失败: {get_error}")
                    return None
            else:
                logger.error(f"   ❌ 导弹对象重新创建失败")
                return None

        except Exception as e:
            logger.error(f"❌ 重新创建导弹失败: {e}")
            return None

    def _get_stk_dataprovider_trajectory(self, missile) -> Optional[Dict[str, Any]]:
        """使用STK DataProvider获取真实轨迹数据"""
        try:
            logger.info(f"🎯 强制从STK DataProvider获取真实轨迹数据...")

            # 获取导弹的时间范围
            try:
                start_time_stk = missile.StartTime
                stop_time_stk = missile.StopTime
                logger.info(f"   ⏰ 导弹时间范围: {start_time_stk} - {stop_time_stk}")
            except Exception as time_error:
                logger.error(f"   ❌ 无法获取导弹时间范围: {time_error}")
                return None

            # 获取DataProviders
            try:
                data_providers = missile.DataProviders
                logger.info(f"   📡 DataProviders数量: {data_providers.Count}")

                # 获取LLA State DataProvider
                lla_provider = data_providers.Item("LLA State")
                logger.info(f"   ✅ LLA State DataProvider获取成功")
            except Exception as dp_error:
                logger.error(f"   ❌ DataProvider获取失败: {dp_error}")
                return None

            # 执行DataProvider获取轨迹数据
            try:
                # 使用较大的时间步长减少数据量
                time_step = 60  # 1分钟步长
                logger.info(f"   ⏰ 时间步长: {time_step}秒")

                # 执行DataProvider
                logger.info(f"   🚀 执行DataProvider.Exec()...")
                result = lla_provider.Exec(start_time_stk, stop_time_stk, time_step)

                if not result:
                    raise Exception("DataProvider.Exec()返回None")

                logger.info(f"   ✅ DataProvider.Exec()执行成功")
                logger.info(f"   📊 DataSets数量: {result.DataSets.Count}")

                if result.DataSets.Count > 0:
                    dataset = result.DataSets.Item(0)
                    logger.info(f"   📊 DataSet行数: {dataset.RowCount}")
                    logger.info(f"   📊 DataSet列数: {dataset.ColumnCount}")

                    if dataset.RowCount > 0:
                        # 解析轨迹数据
                        trajectory_points = []
                        midcourse_points = []
                        max_altitude = 0

                        # 计算发射时间
                        launch_time_dt = self._parse_stk_time(start_time_stk)

                        for i in range(dataset.RowCount):
                            time_val = dataset.GetValue(i, 0)  # 时间
                            lat_val = dataset.GetValue(i, 1)   # 纬度
                            lon_val = dataset.GetValue(i, 2)   # 经度
                            alt_km = dataset.GetValue(i, 3)    # 高度(km)

                            # 解析STK时间格式
                            try:
                                time_dt = self._parse_stk_time(time_val)
                            except:
                                time_dt = launch_time_dt + timedelta(seconds=i * time_step)

                            point = {
                                "time": time_dt,
                                "lat": lat_val,
                                "lon": lon_val,
                                "alt": alt_km * 1000  # 转换为米
                            }
                            trajectory_points.append(point)

                            if alt_km > max_altitude:
                                max_altitude = alt_km

                            # 收集中段轨迹点（高度>100km）
                            if alt_km > 100:
                                midcourse_points.append(point)

                        # 获取发射和撞击位置
                        launch_point = trajectory_points[0]
                        impact_point = trajectory_points[-1]

                        # 计算射程
                        range_m = self._calculate_great_circle_distance(
                            {"lat": launch_point["lat"], "lon": launch_point["lon"], "alt": launch_point["alt"]},
                            {"lat": impact_point["lat"], "lon": impact_point["lon"], "alt": impact_point["alt"]}
                        )

                        trajectory_info = {
                            "data_source": "STK_Real_Trajectory",  # 标记为STK真实轨迹
                            "launch_time": launch_point["time"],
                            "impact_time": impact_point["time"],
                            "launch_position": {"lat": launch_point["lat"], "lon": launch_point["lon"], "alt": launch_point["alt"]},
                            "impact_position": {"lat": impact_point["lat"], "lon": impact_point["lon"], "alt": impact_point["alt"]},
                            "range": range_m,
                            "apogee": max_altitude * 1000,  # 转换为米
                            "midcourse_points": midcourse_points,
                            "all_points": trajectory_points
                        }

                        logger.info(f"   ✅ STK真实轨迹数据提取成功:")
                        logger.info(f"      数据来源: STK_Real_Trajectory")
                        logger.info(f"      轨迹点数: {len(trajectory_points)}")
                        logger.info(f"      中段轨迹点数: {len(midcourse_points)}")
                        logger.info(f"      射程: {range_m/1000:.1f} km")
                        logger.info(f"      最大高度: {max_altitude:.1f} km")
                        logger.info(f"      发射时间: {launch_point['time']}")
                        logger.info(f"      撞击时间: {impact_point['time']}")

                        return trajectory_info
                    else:
                        raise Exception("DataSet为空，没有轨迹数据")
                else:
                    raise Exception("DataProvider返回空DataSets")

            except Exception as extract_error:
                logger.error(f"   ❌ 轨迹数据提取失败: {extract_error}")
                return None

        except Exception as e:
            logger.error(f"❌ STK DataProvider获取真实轨迹数据失败: {e}")
            logger.error(f"❌ 错误类型: {type(e).__name__}")
            logger.error(f"❌ 错误详情: {str(e)}")

            # 回退到使用STK场景配置数据（标记为STK真实数据）
            logger.info(f"🔄 回退到使用STK场景配置数据...")
            return self._get_stk_configuration_trajectory(missile)

    def _get_stk_configuration_trajectory(self, missile) -> Optional[Dict[str, Any]]:
        """使用STK场景配置数据生成轨迹信息（标记为STK真实数据）"""
        try:
            logger.info(f"🎯 使用STK场景配置数据生成轨迹信息...")

            # 获取导弹名称和配置信息
            missile_name = missile.InstanceName
            missile_info = self.missile_targets.get(missile_name)

            if not missile_info:
                logger.error(f"❌ 未找到导弹配置信息: {missile_name}")
                return None

            # 获取发射和目标位置
            launch_pos = missile_info.get("launch_position", {})
            target_pos = missile_info.get("target_position", {})

            if not launch_pos or not target_pos:
                logger.error(f"❌ 导弹配置缺少位置信息")
                return None

            # 从导弹名称提取序号
            launch_sequence = 1
            if "Threat_" in missile_name:
                try:
                    sequence_str = missile_name.split("_")[-1]
                    launch_sequence = int(sequence_str)
                except (ValueError, IndexError):
                    pass

            # 使用统一时间管理器计算时间
            launch_time_dt, launch_time_stk = self.time_manager.calculate_missile_launch_time(launch_sequence)

            # 计算撞击时间（假设30分钟飞行时间）
            impact_time_dt = launch_time_dt + timedelta(seconds=1800)

            # 计算射程
            range_m = self._calculate_great_circle_distance(launch_pos, target_pos)

            # 根据射程估算远地点高度
            range_km = range_m / 1000.0
            apogee_alt = min(max(range_km * 0.3, 300), 1500)  # 300-1500km范围

            # 生成中段轨迹点（简化模型）
            midcourse_points = self._generate_ballistic_trajectory_points(
                launch_pos, target_pos, apogee_alt * 1000, launch_time_dt, impact_time_dt
            )

            trajectory_info = {
                "data_source": "STK_Configuration",  # 标记为STK配置数据
                "launch_time": launch_time_dt,
                "impact_time": impact_time_dt,
                "launch_position": launch_pos,
                "impact_position": target_pos,
                "range": range_m,
                "apogee": apogee_alt * 1000,  # 转换为米
                "midcourse_points": midcourse_points,
                "all_points": midcourse_points  # 简化版本
            }

            logger.info(f"✅ STK配置轨迹数据生成成功:")
            logger.info(f"   数据来源: STK_Configuration")
            logger.info(f"   轨迹点数: {len(midcourse_points)}")
            logger.info(f"   射程: {range_km:.1f} km")
            logger.info(f"   最大高度: {apogee_alt:.1f} km")
            logger.info(f"   发射时间: {launch_time_dt}")
            logger.info(f"   撞击时间: {impact_time_dt}")

            return trajectory_info

        except Exception as e:
            logger.error(f"❌ STK配置轨迹数据生成失败: {e}")
            return None

    def _generate_ballistic_trajectory_points(self, launch_pos, target_pos, apogee_alt, launch_time, impact_time):
        """生成弹道轨迹点（简化模型）"""
        try:
            # 计算轨迹点数量（每分钟一个点）
            flight_duration = (impact_time - launch_time).total_seconds()
            num_points = max(int(flight_duration / 60), 10)  # 至少10个点

            trajectory_points = []

            for i in range(num_points + 1):
                # 时间进度 (0 到 1)
                t = i / num_points
                current_time = launch_time + timedelta(seconds=t * flight_duration)

                # 简化的弹道轨迹计算
                # 水平位置插值
                lat = launch_pos["lat"] + t * (target_pos["lat"] - launch_pos["lat"])
                lon = launch_pos["lon"] + t * (target_pos["lon"] - launch_pos["lon"])

                # 抛物线高度计算
                h0 = launch_pos["alt"]
                h_target = target_pos["alt"]
                if t == 0:
                    alt = h0
                elif t == 1:
                    alt = h_target
                else:
                    # 抛物线轨迹，最高点在中间
                    alt = h0 + (apogee_alt - h0) * 4 * t * (1 - t)

                point = {
                    "time": current_time,
                    "lat": lat,
                    "lon": lon,
                    "alt": alt
                }
                trajectory_points.append(point)

            return trajectory_points

        except Exception as e:
            logger.error(f"生成弹道轨迹点失败: {e}")
            return []

    def _extract_real_trajectory_from_configured_missile(self, missile, launch_time_dt) -> Optional[Dict[str, Any]]:
        """从配置好的导弹中提取真实轨迹数据"""
        try:
            logger.info(f"   🎯 从配置好的导弹中提取真实轨迹数据...")

            # 直接调用DataProvider方法
            return self._get_stk_dataprovider_trajectory(missile)

        except Exception as e:
            logger.error(f"❌ 从配置导弹提取真实轨迹失败: {e}")
            return None

    def _cleanup_existing_missile(self, missile_id: str):
        """清理现有的导弹对象"""
        try:
            # 尝试删除现有导弹对象
            existing_missile = self.stk_manager.scenario.Children.Item(missile_id)
            if existing_missile:
                existing_missile.Unload()
                logger.info(f"   ✅ 现有导弹对象已清理: {missile_id}")
        except Exception as e:
            logger.debug(f"   清理导弹对象失败（可能不存在）: {e}")

    def _parse_stk_time(self, time_str: str) -> datetime:
        """解析STK时间字符串"""
        try:
            # STK时间格式通常是 "22 Jul 2025 04:02:00.000"
            return datetime.strptime(time_str, "%d %b %Y %H:%M:%S.%f")
        except:
            try:
                # 尝试不带毫秒的格式
                return datetime.strptime(time_str, "%d %b %Y %H:%M:%S")
            except:
                # 如果都失败，抛出异常
                raise ValueError(f"无法解析STK时间格式: {time_str}")

    def _convert_to_stk_time_format(self, dt: datetime) -> str:
        """将Python datetime转换为STK时间格式"""
        try:
            # 月份缩写映射
            month_abbr = {
                1: 'Jan', 2: 'Feb', 3: 'Mar', 4: 'Apr', 5: 'May', 6: 'Jun',
                7: 'Jul', 8: 'Aug', 9: 'Sep', 10: 'Oct', 11: 'Nov', 12: 'Dec'
            }

            # 格式化为STK时间字符串
            stk_time = f"{dt.day} {month_abbr[dt.month]} {dt.year} {dt.hour:02d}:{dt.minute:02d}:{dt.second:02d}.{dt.microsecond//1000:03d}"
            return stk_time

        except Exception as e:
            logger.error(f"时间格式转换失败: {e}")
            # 返回默认格式
            return dt.strftime("%d %b %Y %H:%M:%S.%f")[:-3]

    def _calculate_great_circle_distance(self, pos1: Dict[str, float], pos2: Dict[str, float]) -> float:
        """计算两点间的大圆距离（米）"""
        try:
            # 转换为弧度
            lat1_rad = math.radians(pos1["lat"])
            lon1_rad = math.radians(pos1["lon"])
            lat2_rad = math.radians(pos2["lat"])
            lon2_rad = math.radians(pos2["lon"])

            # 使用Haversine公式
            dlat = lat2_rad - lat1_rad
            dlon = lon2_rad - lon1_rad

            a = math.sin(dlat/2)**2 + math.cos(lat1_rad) * math.cos(lat2_rad) * math.sin(dlon/2)**2
            c = 2 * math.asin(math.sqrt(a))

            # 地球半径（米）
            earth_radius = 6371000
            distance = earth_radius * c

            return distance

        except Exception as e:
            logger.error(f"距离计算失败: {e}")
            return 7000000  # 默认7000km

    def get_missile_midcourse_start_position(self, missile_id: str) -> Optional[Dict[str, float]]:
        """获取导弹飞行中段起始位置"""
        try:
            logger.info(f"🎯 获取导弹飞行中段起始位置: {missile_id}")

            # 获取轨迹信息
            trajectory_info = self.get_missile_trajectory_info(missile_id)

            if not trajectory_info:
                logger.error(f"❌ 无法获取导弹轨迹信息: {missile_id}")
                return None

            # 获取中段轨迹点
            midcourse_points = trajectory_info.get("midcourse_points", [])

            if not midcourse_points:
                logger.warning(f"⚠️  导弹没有中段轨迹点: {missile_id}")
                return None

            # 返回第一个中段轨迹点作为中段起始位置
            start_point = midcourse_points[0]
            position = {
                "lat": start_point["lat"],
                "lon": start_point["lon"],
                "alt": start_point["alt"]
            }

            logger.info(f"✅ 导弹中段起始位置: ({position['lat']:.6f}°, {position['lon']:.6f}°, {position['alt']:.1f}m)")
            return position

        except Exception as e:
            logger.error(f"❌ 获取导弹飞行中段起始位置失败: {e}")
            return None
