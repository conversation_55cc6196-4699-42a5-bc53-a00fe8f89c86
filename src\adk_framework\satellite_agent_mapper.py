#!/usr/bin/env python3
"""
卫星-智能体映射管理器 V1.0
基于星座配置系统，实现一对一的卫星-智能体映射关系
融合到V1.0版本的ADK框架
"""

import logging
import uuid
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple
import json

from .enhanced_adk_agent import EnhancedADKSatelliteAgent
from .distributed_agent_manager import DistributedAgentManager

logger = logging.getLogger(__name__)

class SatelliteAgentMapper:
    """
    卫星-智能体映射管理器 V1.0
    负责建立和维护卫星与智能体的一对一映射关系
    """
    
    def __init__(self, constellation_config, stk_manager=None):
        """
        初始化映射管理器
        
        Args:
            constellation_config: 星座配置字典
            stk_manager: STK管理器
        """
        self.constellation_config = constellation_config
        self.stk_manager = stk_manager
        
        # 映射关系
        self.satellite_agent_mapping = {}  # {satellite_name: agent_instance}
        self.agent_satellite_mapping = {}  # {agent_id: satellite_name}
        self.satellite_metadata = {}       # {satellite_name: metadata}
        
        # 分布式智能体管理器
        self.agent_manager = None
        
        # 映射统计
        self.mapping_stats = {
            "total_satellites": 0,
            "mapped_agents": 0,
            "active_agents": 0,
            "created_time": datetime.now().isoformat()
        }
        
        logger.info("🗺️  卫星-智能体映射管理器V1.0初始化完成")
    
    def generate_satellite_list(self) -> List[Dict[str, Any]]:
        """
        基于星座配置生成卫星列表
        
        Returns:
            卫星列表，每个卫星包含完整的配置信息
        """
        try:
            satellites = []
            planes = self.constellation_config.get("planes", 3)
            satellites_per_plane = self.constellation_config.get("satellites_per_plane", 3)
            
            logger.info(f"🛰️  生成卫星列表: {planes}个轨道面，每面{satellites_per_plane}颗卫星")
            
            for plane_idx in range(planes):
                for sat_idx in range(satellites_per_plane):
                    # 生成卫星名称
                    satellite_name = f"Satellite{plane_idx+1}{sat_idx+1}"
                    
                    # 生成卫星配置
                    satellite_config = self._generate_satellite_config(
                        satellite_name, plane_idx, sat_idx
                    )
                    
                    satellites.append(satellite_config)
            
            self.mapping_stats["total_satellites"] = len(satellites)
            logger.info(f"✅ 生成了 {len(satellites)} 颗卫星的配置")
            
            return satellites
            
        except Exception as e:
            logger.error(f"❌ 生成卫星列表失败: {e}")
            return []
    
    def _generate_satellite_config(self, satellite_name: str, plane_idx: int, 
                                 sat_idx: int) -> Dict[str, Any]:
        """生成单个卫星的配置"""
        try:
            # 基础轨道参数
            reference_satellite = self.constellation_config.get("reference_satellite", {})
            
            # 计算轨道参数
            altitude = reference_satellite.get("altitude", 1800)
            inclination = reference_satellite.get("inclination", 51.856)
            
            # 计算RAAN（升交点赤经）
            raan_offset = reference_satellite.get("raan_offset", 24)
            raan = plane_idx * (360 / self.constellation_config.get("planes", 3))
            
            # 计算平近点角
            mean_anomaly_offset = reference_satellite.get("mean_anomaly_offset", 180)
            mean_anomaly = sat_idx * (360 / self.constellation_config.get("satellites_per_plane", 3))
            
            # 智能分配能力
            capabilities = self._assign_satellite_capabilities(plane_idx, sat_idx)
            
            # 智能分配资源
            resources = self._assign_satellite_resources(plane_idx, sat_idx)
            
            # 分配角色
            role = self._assign_satellite_role(plane_idx, sat_idx)
            
            satellite_config = {
                "satellite_name": satellite_name,
                "satellite_id": satellite_name,
                "orbital_plane": plane_idx + 1,
                "position_in_plane": sat_idx + 1,
                
                # 轨道参数
                "orbital_parameters": {
                    "altitude": altitude,
                    "inclination": inclination,
                    "eccentricity": reference_satellite.get("eccentricity", 0.0),
                    "arg_of_perigee": reference_satellite.get("arg_of_perigee", 12),
                    "raan": raan,
                    "mean_anomaly": mean_anomaly,
                    "latitude": 0.0,  # 简化假设
                    "longitude": raan,
                    "altitude_m": altitude * 1000
                },
                
                # 智能体配置
                "capabilities": capabilities,
                "resources": resources,
                "role": role,
                
                # 元数据
                "metadata": {
                    "created_time": datetime.now().isoformat(),
                    "plane_index": plane_idx,
                    "satellite_index": sat_idx,
                    "total_planes": self.constellation_config.get("planes", 3),
                    "satellites_per_plane": self.constellation_config.get("satellites_per_plane", 3)
                }
            }
            
            # 存储元数据
            self.satellite_metadata[satellite_name] = satellite_config
            
            return satellite_config
            
        except Exception as e:
            logger.error(f"❌ 生成卫星配置失败: {e}")
            return {}
    
    def _assign_satellite_capabilities(self, plane_idx: int, sat_idx: int) -> List[str]:
        """智能分配卫星能力"""
        base_capabilities = ["coordination"]
        
        # 基于轨道面分配专业能力
        if plane_idx == 0:  # 轨道面1: ICBM检测组
            base_capabilities.extend(["icbm_detection", "threat_assessment", "early_warning"])
        elif plane_idx == 1:  # 轨道面2: 监视组
            base_capabilities.extend(["surveillance", "data_relay", "early_warning"])
        else:  # 轨道面3: 备份组
            base_capabilities.extend(["backup", "communication", "support"])
        
        # 第一颗卫星具备领导能力
        if sat_idx == 0:
            base_capabilities.append("leadership")
        
        # 前两颗卫星具备跟踪能力
        if sat_idx < 2:
            base_capabilities.append("tracking")
        
        return base_capabilities
    
    def _assign_satellite_resources(self, plane_idx: int, sat_idx: int) -> Dict[str, float]:
        """智能分配卫星资源"""
        base_resources = {
            "energy": 85.0 + (sat_idx * 5),  # 85-95%
            "storage": 70.0 + (plane_idx * 10),  # 70-90%
            "bandwidth": 20.0 + (sat_idx * 5),  # 20-30 Mbps
            "health": 100.0
        }
        
        return base_resources
    
    def _assign_satellite_role(self, plane_idx: int, sat_idx: int) -> str:
        """分配卫星角色"""
        if sat_idx == 0:
            return "leader_candidate"
        elif plane_idx < 2:  # 轨道面1和2
            return "coordinator"
        else:  # 轨道面3
            return "support"
    
    async def create_satellite_agent_mapping(self) -> bool:
        """
        创建卫星-智能体映射
        
        Returns:
            bool: 创建是否成功
        """
        try:
            logger.info("🚀 开始创建卫星-智能体映射")
            
            # 1. 生成卫星列表
            satellites = self.generate_satellite_list()
            if not satellites:
                logger.error("❌ 卫星列表为空，无法创建映射")
                return False
            
            # 2. 创建分布式智能体管理器
            self.agent_manager = DistributedAgentManager()
            
            # 3. 为每颗卫星创建对应的智能体
            successful_mappings = 0
            
            for satellite_config in satellites:
                satellite_name = satellite_config["satellite_name"]
                
                try:
                    # 创建ADK智能体
                    agent = EnhancedADKSatelliteAgent(
                        satellite_id=satellite_name,
                        config=satellite_config,
                        stk_manager=self.stk_manager
                    )
                    
                    # 添加到分布式管理器
                    agent_id = await self.agent_manager.add_agent(agent)
                    
                    # 建立映射关系
                    self.satellite_agent_mapping[satellite_name] = agent
                    self.agent_satellite_mapping[agent_id] = satellite_name
                    
                    successful_mappings += 1
                    logger.info(f"✅ 卫星 {satellite_name} 映射到智能体 {agent_id}")
                    
                except Exception as e:
                    logger.error(f"❌ 卫星 {satellite_name} 映射失败: {e}")
            
            # 4. 更新统计信息
            self.mapping_stats.update({
                "mapped_agents": successful_mappings,
                "active_agents": successful_mappings,
                "mapping_success_rate": successful_mappings / len(satellites) * 100
            })
            
            logger.info(f"🎉 卫星-智能体映射完成:")
            logger.info(f"   总卫星数: {len(satellites)}")
            logger.info(f"   成功映射: {successful_mappings}")
            logger.info(f"   成功率: {self.mapping_stats['mapping_success_rate']:.1f}%")
            
            return successful_mappings > 0
            
        except Exception as e:
            logger.error(f"❌ 创建卫星-智能体映射失败: {e}")
            return False
    
    def get_agent_by_satellite(self, satellite_name: str) -> Optional[EnhancedADKSatelliteAgent]:
        """根据卫星名称获取智能体"""
        return self.satellite_agent_mapping.get(satellite_name)
    
    def get_satellite_by_agent(self, agent_id: str) -> Optional[str]:
        """根据智能体ID获取卫星名称"""
        return self.agent_satellite_mapping.get(agent_id)
    
    def get_agents_by_capability(self, capability: str) -> List[EnhancedADKSatelliteAgent]:
        """根据能力获取智能体列表"""
        agents = []
        for satellite_name, agent in self.satellite_agent_mapping.items():
            if hasattr(agent, 'config') and capability in agent.config.get('capabilities', []):
                agents.append(agent)
        return agents
    
    def get_agents_by_orbital_plane(self, plane_idx: int) -> List[EnhancedADKSatelliteAgent]:
        """根据轨道面获取智能体列表"""
        agents = []
        for satellite_name, agent in self.satellite_agent_mapping.items():
            if hasattr(agent, 'config') and agent.config.get('orbital_plane') == plane_idx:
                agents.append(agent)
        return agents
    
    def get_mapping_statistics(self) -> Dict[str, Any]:
        """获取映射统计信息"""
        return self.mapping_stats.copy()
    
    def get_satellite_metadata(self, satellite_name: str) -> Optional[Dict[str, Any]]:
        """获取卫星的元数据"""
        return self.satellite_metadata.get(satellite_name)
    
    def get_all_agents(self) -> Dict[str, Any]:
        """获取所有智能体"""
        if self.agent_manager:
            return self.agent_manager.agents
        return {}
    
    async def process_target_with_mapping(self, target_info: Dict[str, Any]) -> Dict[str, Any]:
        """使用映射关系处理目标"""
        try:
            logger.info(f"🎯 使用卫星-智能体映射处理目标: {target_info.get('target_id')}")
            
            if not self.agent_manager:
                logger.error("❌ 智能体管理器未初始化")
                return {"success": False, "error": "智能体管理器未初始化"}
            
            # 使用分布式管理器处理目标
            result = await self.agent_manager.process_target(target_info)
            
            return result
            
        except Exception as e:
            logger.error(f"❌ 映射处理目标失败: {e}")
            return {"success": False, "error": str(e)}
