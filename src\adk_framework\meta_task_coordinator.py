"""
元任务协调器 V1.0
负责原子任务分解、距离计算和组长选举
融合到V1.0版本
"""

import logging
import math
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
import uuid

logger = logging.getLogger(__name__)

class MetaTaskCoordinator:
    """元任务协调器 V1.0 - 负责任务分解和组长选举"""
    
    def __init__(self, stk_manager=None):
        """初始化元任务协调器
        
        Args:
            stk_manager: STK管理器实例
        """
        self.stk_manager = stk_manager
        self.earth_radius = 6371000  # 地球半径（米）
        self.atomic_task_duration = 60  # 原子任务时长（秒）
        self.high_altitude_threshold = 100000  # 高空阈值（米）
        
        logger.info("元任务协调器V1.0初始化完成")
    
    def decompose_target_to_atomic_tasks(self, target_info: Dict[str, Any]) -> Dict[str, Any]:
        """
        将目标分解为原子任务
        
        Args:
            target_info: 目标信息
            
        Returns:
            Dict[str, Any]: 包含原子任务的目标信息
        """
        try:
            target_id = target_info.get("target_id", "Unknown")
            logger.info(f"🔍 开始原子任务分解: {target_id}")
            
            # 如果已经有原子任务，直接返回
            if target_info.get("atomic_tasks"):
                logger.info(f"目标 {target_id} 已包含原子任务信息")
                return target_info
            
            # 获取时间信息
            launch_time_str = target_info.get("launch_time")
            impact_time_str = target_info.get("impact_time")
            
            if not launch_time_str or not impact_time_str:
                logger.warning(f"目标 {target_id} 缺少时间信息")
                return target_info
            
            # 解析时间
            if isinstance(launch_time_str, str):
                launch_time = datetime.fromisoformat(launch_time_str.replace('Z', '+00:00'))
            else:
                launch_time = launch_time_str
                
            if isinstance(impact_time_str, str):
                impact_time = datetime.fromisoformat(impact_time_str.replace('Z', '+00:00'))
            else:
                impact_time = impact_time_str
            
            # 计算高空飞行段（发射后2分钟到落弹前2分钟）
            tracking_start_time = launch_time + timedelta(minutes=2)
            tracking_end_time = impact_time - timedelta(minutes=2)
            tracking_duration = (tracking_end_time - tracking_start_time).total_seconds()
            
            if tracking_duration <= 0:
                logger.warning(f"目标 {target_id} 跟踪时长无效")
                return target_info
            
            # 生成原子任务
            num_atomic_tasks = max(1, int(tracking_duration // self.atomic_task_duration))
            atomic_tasks = []
            
            for i in range(num_atomic_tasks):
                task_start = tracking_start_time + timedelta(seconds=i * self.atomic_task_duration)
                
                if i == num_atomic_tasks - 1:
                    task_end = tracking_end_time
                else:
                    task_end = tracking_start_time + timedelta(seconds=(i + 1) * self.atomic_task_duration)
                
                task_duration = (task_end - task_start).total_seconds()
                
                atomic_tasks.append({
                    "atomic_task_id": i + 1,
                    "start_time": task_start,
                    "end_time": task_end,
                    "duration": task_duration
                })
            
            # 更新目标信息
            enhanced_target_info = target_info.copy()
            enhanced_target_info.update({
                "tracking_task": {
                    "start_time": tracking_start_time,
                    "end_time": tracking_end_time,
                    "duration": tracking_duration,
                    "max_altitude": target_info.get("trajectory", {}).get("max_altitude", 300000),
                    "altitude_threshold": self.high_altitude_threshold
                },
                "atomic_tasks": atomic_tasks,
                "atomic_task_duration": self.atomic_task_duration,
                "total_atomic_tasks": len(atomic_tasks)
            })
            
            logger.info(f"✅ 原子任务分解完成:")
            logger.info(f"   目标: {target_id}")
            logger.info(f"   跟踪时长: {tracking_duration:.0f}秒")
            logger.info(f"   原子任务数: {len(atomic_tasks)}个")
            
            return enhanced_target_info
            
        except Exception as e:
            logger.error(f"❌ 原子任务分解失败: {e}")
            return target_info
    
    def find_nearest_satellite_to_target(self, target_info: Dict[str, Any], 
                                       satellite_positions: Dict[str, Tuple[float, float, float]]) -> Optional[str]:
        """
        找到距离目标最近的卫星
        
        Args:
            target_info: 目标信息
            satellite_positions: 卫星位置字典 {satellite_id: (lat, lon, alt)}
            
        Returns:
            Optional[str]: 最近卫星的ID，如果没有找到则返回None
        """
        try:
            target_position = target_info.get("target_position")
            if not target_position:
                # 如果没有目标位置，使用轨迹中点
                trajectory = target_info.get("trajectory", {})
                launch_site = trajectory.get("launch_site", {})
                impact_site = trajectory.get("impact_site", {})
                
                if launch_site and impact_site:
                    target_lat = (launch_site.get("lat", 0) + impact_site.get("lat", 0)) / 2
                    target_lon = (launch_site.get("lon", 0) + impact_site.get("lon", 0)) / 2
                    target_alt = trajectory.get("max_altitude", 300000)
                else:
                    logger.warning("目标信息中没有位置信息")
                    return None
            else:
                target_lat = target_position.get("latitude", 0.0)
                target_lon = target_position.get("longitude", 0.0)
                target_alt = target_position.get("altitude", 0.0)
            
            min_distance = float('inf')
            nearest_satellite = None
            
            for sat_id, (sat_lat, sat_lon, sat_alt) in satellite_positions.items():
                distance = self._calculate_3d_distance(
                    (target_lat, target_lon, target_alt),
                    (sat_lat, sat_lon, sat_alt)
                )
                
                if distance < min_distance:
                    min_distance = distance
                    nearest_satellite = sat_id
            
            if nearest_satellite:
                logger.info(f"🎯 找到最近卫星:")
                logger.info(f"   卫星: {nearest_satellite}")
                logger.info(f"   距离: {min_distance/1000:.1f}km")
            
            return nearest_satellite
            
        except Exception as e:
            logger.error(f"❌ 查找最近卫星失败: {e}")
            return None
    
    def _calculate_3d_distance(self, pos1: Tuple[float, float, float], 
                              pos2: Tuple[float, float, float]) -> float:
        """
        计算两个3D位置之间的距离
        
        Args:
            pos1: 位置1 (lat, lon, alt)
            pos2: 位置2 (lat, lon, alt)
            
        Returns:
            float: 距离（米）
        """
        try:
            lat1, lon1, alt1 = pos1
            lat2, lon2, alt2 = pos2
            
            # 转换为弧度
            lat1_rad = math.radians(lat1)
            lon1_rad = math.radians(lon1)
            lat2_rad = math.radians(lat2)
            lon2_rad = math.radians(lon2)
            
            # 计算球面距离
            dlat = lat2_rad - lat1_rad
            dlon = lon2_rad - lon1_rad
            
            a = math.sin(dlat/2)**2 + math.cos(lat1_rad) * math.cos(lat2_rad) * math.sin(dlon/2)**2
            c = 2 * math.atan2(math.sqrt(a), math.sqrt(1-a))
            
            # 地面距离
            ground_distance = self.earth_radius * c
            
            # 考虑高度差的3D距离
            alt_diff = abs(alt1 - alt2)
            distance_3d = math.sqrt(ground_distance**2 + alt_diff**2)
            
            return distance_3d
            
        except Exception as e:
            logger.error(f"距离计算失败: {e}")
            return float('inf')
    
    def should_satellite_be_leader(self, satellite_id: str, target_info: Dict[str, Any],
                                 satellite_positions: Dict[str, Tuple[float, float, float]],
                                 satellite_capabilities: Dict[str, List[str]]) -> bool:
        """
        判断卫星是否应该成为组长
        
        Args:
            satellite_id: 卫星ID
            target_info: 目标信息
            satellite_positions: 卫星位置字典
            satellite_capabilities: 卫星能力字典
            
        Returns:
            bool: 是否应该成为组长
        """
        try:
            # 1. 检查卫星是否具备领导能力
            capabilities = satellite_capabilities.get(satellite_id, [])
            has_leadership = "leadership" in capabilities
            has_detection = "icbm_detection" in capabilities
            
            if not (has_leadership or has_detection):
                return False
            
            # 2. 找到距离目标最近的卫星
            nearest_satellite = self.find_nearest_satellite_to_target(target_info, satellite_positions)
            
            # 3. 如果当前卫星是最近的，则成为组长
            is_nearest = satellite_id == nearest_satellite
            
            logger.info(f"🔍 组长选举评估 - {satellite_id}:")
            logger.info(f"   领导能力: {has_leadership}")
            logger.info(f"   检测能力: {has_detection}")
            logger.info(f"   最近卫星: {nearest_satellite}")
            logger.info(f"   是否最近: {is_nearest}")
            
            return is_nearest
            
        except Exception as e:
            logger.error(f"❌ 组长选举评估失败: {e}")
            return False
    
    async def coordinate_target_assignment(self, target_info: Dict[str, Any],
                                         satellite_agents: Dict[str, Any]) -> Dict[str, Any]:
        """
        协调目标分配 - 找到最近卫星并分配任务
        
        Args:
            target_info: 目标信息
            satellite_agents: 卫星智能体字典
            
        Returns:
            Dict[str, Any]: 分配结果
        """
        try:
            target_id = target_info.get("target_id", "Unknown")
            logger.info(f"🎯 开始目标分配协调: {target_id}")
            
            # 1. 分解原子任务
            enhanced_target_info = self.decompose_target_to_atomic_tasks(target_info)
            
            # 2. 获取卫星位置（简化版本）
            satellite_positions = {}
            satellite_capabilities = {}
            
            for sat_id, agent in satellite_agents.items():
                # 简化的位置获取（实际应该从STK获取）
                config = getattr(agent, 'config', {})
                orbital_params = config.get('orbital_parameters', {})
                
                # 使用配置中的位置信息或默认值
                lat = orbital_params.get('latitude', 0.0)
                lon = orbital_params.get('longitude', 0.0) 
                alt = orbital_params.get('altitude_m', 1800000)
                
                satellite_positions[sat_id] = (lat, lon, alt)
                satellite_capabilities[sat_id] = config.get('capabilities', [])
            
            # 3. 找到最近的卫星
            nearest_satellite = self.find_nearest_satellite_to_target(enhanced_target_info, satellite_positions)
            
            if not nearest_satellite:
                logger.warning(f"未找到目标 {target_id} 的最近卫星")
                return {"success": False, "error": "未找到最近卫星"}
            
            # 4. 向最近的卫星发送任务
            nearest_agent = satellite_agents.get(nearest_satellite)
            if not nearest_agent:
                logger.warning(f"最近卫星 {nearest_satellite} 的智能体不存在")
                return {"success": False, "error": "最近卫星智能体不存在"}
            
            # 5. 发送任务给最近的智能体
            assignment_result = await nearest_agent.receive_missile_target(enhanced_target_info)
            
            logger.info(f"✅ 目标分配完成:")
            logger.info(f"   目标: {target_id}")
            logger.info(f"   分配给: {nearest_satellite}")
            logger.info(f"   分配结果: {assignment_result.get('success', False)}")
            
            return {
                "success": True,
                "target_id": target_id,
                "assigned_to": nearest_satellite,
                "assignment_result": assignment_result,
                "enhanced_target_info": enhanced_target_info
            }
            
        except Exception as e:
            logger.error(f"❌ 目标分配协调失败: {e}")
            return {"success": False, "error": str(e)}
