#!/usr/bin/env python3
"""
增强版ADK卫星智能体 V1.0 - 融合成功的ADK框架
基于之前成功的ADK框架实现，融合到V1.0版本
保持与V1.0版本的兼容性
"""

import asyncio
import logging
import uuid
import json
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional

# 必须使用ADK框架 - 使用正确的导入路径
try:
    from google.adk import Agent as LlmAgent
    from google.adk.tools import FunctionTool
    from google.adk.sessions import Session
    from google.adk.memory import InMemoryMemoryService
    ADK_AVAILABLE = True
    print("✅ ADK框架加载成功 (google.adk)")
except ImportError as e:
    print(f"❌ ADK框架导入失败: {e}")
    print("请确保已正确安装ADK框架: pip install google-adk")
    raise ImportError("ADK框架是必需的，无法回退到模拟模式")

# 备用LLM支持
try:
    import litellm
    LITELLM_AVAILABLE = True
except ImportError:
    LITELLM_AVAILABLE = False

logger = logging.getLogger(__name__)

class EnhancedADKSatelliteAgent:
    """
    增强版ADK卫星智能体 V1.0 - 融合成功的ADK框架
    基于之前成功的ADK框架实现，支持工具调用和分布式协调
    保持与V1.0版本的兼容性
    """
    
    def __init__(self, satellite_id: str, config: Dict[str, Any], 
                 stk_manager=None, visibility_calculator=None, output_manager=None):
        """
        初始化增强版ADK卫星智能体 - V1.0兼容版本
        
        Args:
            satellite_id: 卫星ID
            config: 卫星配置
            stk_manager: STK管理器
            visibility_calculator: 可见性计算器 (V1.0兼容)
            output_manager: 输出管理器 (V1.0兼容)
        """
        self.satellite_id = satellite_id
        self.config = config
        self.stk_manager = stk_manager
        self.visibility_calculator = visibility_calculator
        self.output_manager = output_manager
        self.name = satellite_id  # V1.0兼容性
        
        # ADK智能体
        self._llm_agent = None
        self.llm_client = None
        
        # V1.0兼容性属性
        self.adk_agent = None
        self.session = None
        self.memory_service = None
        self.invocation_context = None
        
        # 智能体状态 - 融合V1.0和新版本
        self._agent_state = {
            "satellite_id": satellite_id,
            "status": "active",
            "current_tasks": [],
            "pending_tasks": [],
            "completed_tasks": [],
            "discussion_groups": [],
            "is_leader": False,
            "energy": config.get("resources", {}).get("energy", 100),
            "storage": config.get("resources", {}).get("storage", 100),
            "health": config.get("resources", {}).get("health", 100)
        }
        
        # 记忆模块
        self._memory = {}
        
        # V1.0兼容性 - 任务管理
        self.current_tasks = {}
        self.task_history = []
        self.coordination_groups = {}
        
        # V1.0兼容性 - 分布式组网
        self.network = None
        self.peer_agents = {}
        self.discussion_groups = {}
        
        # 初始化智能体
        self._initialize_agent()
        
        logger.info(f"🤖 增强ADK卫星智能体V1.0 {satellite_id} 初始化完成")
    
    def _initialize_agent(self):
        """初始化ADK智能体 - 强制使用ADK框架"""
        try:
            # 必须使用ADK框架
            if not ADK_AVAILABLE:
                raise RuntimeError("ADK框架不可用，无法创建智能体")

            logger.info(f"🚀 开始初始化ADK智能体: {self.satellite_id}")

            # 1. 创建ADK工具
            tools = self._create_adk_tools()
            logger.info(f"✅ 创建了 {len(tools)} 个ADK工具")

            # 2. 构建智能体指令
            instruction = self._build_agent_instruction()
            logger.info(f"✅ 构建智能体指令完成")

            # 3. 创建ADK智能体
            self._llm_agent = LlmAgent(
                instruction=instruction,
                tools=tools,
                model="gpt-4o-mini"  # 使用经济模型
            )

            # 4. V1.0兼容性
            self.adk_agent = self._llm_agent

            # 5. 创建会话和记忆服务
            self.session = Session(
                id=f"session_{self.satellite_id}",
                app_name="ICBM_Warning_System",
                user_id=self.satellite_id
            )

            self.memory_service = InMemoryMemoryService()

            logger.info(f"✅ ADK智能体 {self.satellite_id} 初始化成功")
            logger.info(f"   - 工具数量: {len(tools)}")
            logger.info(f"   - 模型: gpt-4o-mini")
            logger.info(f"   - 会话ID: {self.session.id}")

        except Exception as e:
            logger.error(f"❌ 初始化ADK智能体失败: {e}")
            raise RuntimeError(f"ADK智能体初始化失败: {e}")
    
    def _create_adk_tools(self) -> List:
        """创建ADK工具函数 - 使用正确的ADK工具格式"""
        tools = []

        try:
            # 使用ADK的FunctionTool包装Python函数
            tools = [
                FunctionTool(self._tool_calculate_visibility),
                FunctionTool(self._tool_send_coordination_message),
                FunctionTool(self._tool_create_discussion_group),
                FunctionTool(self._tool_query_memory),
                FunctionTool(self._tool_update_task_status)
            ]

            logger.info(f"✅ 创建了 {len(tools)} 个ADK FunctionTool")
            return tools

        except Exception as e:
            logger.error(f"❌ 创建ADK工具失败: {e}")
            # 如果FunctionTool失败，尝试直接传递函数
            try:
                tools = [
                    self._tool_calculate_visibility,
                    self._tool_send_coordination_message,
                    self._tool_create_discussion_group,
                    self._tool_query_memory,
                    self._tool_update_task_status
                ]
                logger.info(f"✅ 回退到直接函数传递，创建了 {len(tools)} 个工具")
                return tools
            except Exception as e2:
                logger.error(f"❌ 工具创建完全失败: {e2}")
                return []
    
    def _build_agent_instruction(self) -> str:
        """构建智能体指令 - 基于ADK框架的工具调用"""
        capabilities = self.config.get("capabilities", [])
        resources = self.config.get("resources", {})
        
        return f"""
你是卫星智能体 {self.satellite_id}，负责以下任务：

1. 任务管理：
   - 使用 query_memory 工具查询记忆模块中正在执行的任务列表
   - 将收到的导弹目标信息加入未执行任务列表
   - 使用 update_task_status 工具更新任务状态
   - 通过大模型推理结合系统优化目标给出结果

2. 协同工作：
   - 参与讨论组，与其他卫星智能体协调
   - 如果成为组长，使用 calculate_visibility 工具计算可见窗口
   - 使用 create_discussion_group 工具创建讨论组并管理
   - 使用 send_coordination_message 工具发送协调消息
   - 将协调成功的任务加入执行任务清单

3. 优化目标：
   - 最大化跟踪精度（最小化GDOP值）
   - 提高系统鲁棒性
   - 平衡能源和存储资源使用

可用工具：
- calculate_visibility(target_id): 计算对目标的可见性窗口
- send_coordination_message(target_satellites, message, priority): 发送协调消息
- create_discussion_group(target_id, participants, topic): 创建讨论组
- query_memory(query_type, keyword): 查询记忆模块信息
- update_task_status(task_id, new_status, details): 更新任务状态

当前卫星状态：
- 能力: {', '.join(capabilities)}
- 能源: {resources.get('energy', 100):.1f}%
- 存储: {resources.get('storage', 100):.1f}%

请根据接收到的任务信息和当前状态，使用可用的工具函数做出最优决策。
"""
    
    # ADK工具函数实现 - 基于ADK官方文档的正确方式
    def _tool_calculate_visibility(self, target_id: str) -> str:
        """
        计算对目标的可见性窗口
        
        Args:
            target_id (str): 目标ID，例如 "ICBM_001"
            
        Returns:
            str: 可见性计算结果的JSON字符串
        """
        try:
            logger.info(f"🔍 {self.satellite_id} 计算对目标 {target_id} 的可见性")
            
            if self.stk_manager:
                # 使用STK进行真实计算
                visibility_result = self.stk_manager.calculate_visibility(
                    self.satellite_id, {"target_id": target_id}
                )
            else:
                # 模拟可见性计算
                visibility_result = {
                    "has_visibility": True,
                    "start_time": datetime.now().isoformat(),
                    "end_time": (datetime.now() + timedelta(minutes=30)).isoformat(),
                    "max_elevation": 45.0,
                    "quality_score": 0.8,
                    "calculation_type": "simulated"
                }
            
            # 存储到记忆
            self._memory[f"visibility_{target_id}"] = {
                "target_id": target_id,
                "visibility_result": visibility_result,
                "calculated_time": datetime.now().isoformat()
            }
            
            result = {
                "success": True,
                "satellite_id": self.satellite_id,
                "target_id": target_id,
                "visibility_result": visibility_result
            }
            
            return json.dumps(result)
            
        except Exception as e:
            logger.error(f"❌ 可见性计算失败: {e}")
            error_result = {"success": False, "error": str(e)}
            return json.dumps(error_result)
    
    def _tool_send_coordination_message(self, target_satellites: str, 
                                       message: str, priority: str = "normal") -> str:
        """
        向其他卫星发送协调消息
        
        Args:
            target_satellites (str): 目标卫星列表，用逗号分隔
            message (str): 协调消息内容
            priority (str): 消息优先级
            
        Returns:
            str: 发送结果的JSON字符串
        """
        try:
            # 解析目标卫星列表
            target_list = [sat.strip() for sat in target_satellites.split(",")]
            logger.info(f"📡 {self.satellite_id} 发送协调消息给 {target_list}")
            
            message_data = {
                "type": "coordination_message",
                "source": self.satellite_id,
                "targets": target_list,
                "content": message,
                "priority": priority,
                "timestamp": datetime.now().isoformat(),
                "message_id": f"msg_{uuid.uuid4().hex[:8]}"
            }
            
            # 模拟发送
            result = {
                "success": True,
                "message_id": message_data["message_id"],
                "sent_to": len(target_list),
                "total_targets": len(target_list),
                "mode": "simulated"
            }
            
            return json.dumps(result)
                
        except Exception as e:
            logger.error(f"❌ 发送协调消息失败: {e}")
            error_result = {"success": False, "error": str(e)}
            return json.dumps(error_result)
    
    def _tool_create_discussion_group(self, target_id: str, 
                                     participants: str, topic: str) -> str:
        """
        创建多星讨论组
        
        Args:
            target_id (str): 目标ID
            participants (str): 参与者列表，用逗号分隔
            topic (str): 讨论主题
            
        Returns:
            str: 创建结果的JSON字符串
        """
        try:
            # 解析参与者列表
            participant_list = [p.strip() for p in participants.split(",")]
            discussion_id = f"discussion_{target_id}_{uuid.uuid4().hex[:8]}"
            
            logger.info(f"💬 {self.satellite_id} 创建讨论组 {discussion_id}")
            
            discussion_group = {
                "discussion_id": discussion_id,
                "target_id": target_id,
                "leader_id": self.satellite_id,
                "participants": participant_list,
                "topic": topic,
                "created_time": datetime.now().isoformat(),
                "status": "active",
                "messages": []
            }
            
            # 添加到智能体状态
            self._agent_state["discussion_groups"].append(discussion_group)
            self._agent_state["is_leader"] = True
            
            # V1.0兼容性
            self.discussion_groups[discussion_id] = discussion_group
            
            # 存储到记忆
            self._memory[f"discussion_{discussion_id}"] = discussion_group
            
            result = {
                "success": True,
                "discussion_id": discussion_id,
                "participants_count": len(participant_list),
                "leader_id": self.satellite_id,
                "participants": participant_list
            }
            
            return json.dumps(result)
            
        except Exception as e:
            logger.error(f"❌ 创建讨论组失败: {e}")
            error_result = {"success": False, "error": str(e)}
            return json.dumps(error_result)

    def _tool_query_memory(self, query_type: str, keyword: str = "") -> str:
        """
        查询记忆模块中的信息

        Args:
            query_type (str): 查询类型
            keyword (str): 查询关键词

        Returns:
            str: 查询结果的JSON字符串
        """
        try:
            logger.info(f"🧠 {self.satellite_id} 查询记忆: {query_type}")

            results = []

            if query_type == "tasks":
                # 查询任务相关记忆
                for key, value in self._memory.items():
                    if key.startswith("task_") or key.startswith("target_"):
                        results.append(value)

            elif query_type == "visibility":
                # 查询可见性相关记忆
                for key, value in self._memory.items():
                    if key.startswith("visibility_"):
                        results.append(value)

            elif query_type == "coordination":
                # 查询协调相关记忆
                for key, value in self._memory.items():
                    if key.startswith("coordination_") or key.startswith("discussion_"):
                        results.append(value)

            elif query_type == "all":
                # 查询所有记忆
                results = list(self._memory.values())

            # 如果有关键词，进行过滤
            if keyword:
                filtered_results = []
                for result in results:
                    if keyword.lower() in str(result).lower():
                        filtered_results.append(result)
                results = filtered_results

            # 添加当前智能体状态信息
            current_state = {
                "current_tasks": len(self._agent_state.get("current_tasks", [])),
                "pending_tasks": len(self._agent_state.get("pending_tasks", [])),
                "completed_tasks": len(self._agent_state.get("completed_tasks", [])),
                "discussion_groups": len(self._agent_state.get("discussion_groups", [])),
                "is_leader": self._agent_state.get("is_leader", False),
                "energy": self._agent_state.get("energy", 100),
                "storage": self._agent_state.get("storage", 100)
            }

            query_result = {
                "success": True,
                "query_type": query_type,
                "keyword": keyword,
                "results_count": len(results),
                "results": results[:10],  # 限制返回数量
                "current_state": current_state
            }

            return json.dumps(query_result)

        except Exception as e:
            logger.error(f"❌ 查询记忆失败: {e}")
            error_result = {"success": False, "error": str(e)}
            return json.dumps(error_result)

    def _tool_update_task_status(self, task_id: str, new_status: str,
                               details: str = "") -> str:
        """
        更新任务执行状态

        Args:
            task_id (str): 任务ID
            new_status (str): 新状态
            details (str): 更新详情

        Returns:
            str: 更新结果的JSON字符串
        """
        try:
            logger.info(f"📋 {self.satellite_id} 更新任务状态: {task_id} -> {new_status}")

            # 在不同状态列表中查找和更新任务
            task_found = False
            task_lists = ["pending_tasks", "current_tasks", "completed_tasks"]

            for list_name in task_lists:
                if list_name in self._agent_state:
                    task_list = self._agent_state[list_name]
                    for i, task in enumerate(task_list):
                        if task.get("task_id") == task_id:
                            # 更新任务状态
                            task["status"] = new_status
                            task["last_updated"] = datetime.now().isoformat()
                            if details:
                                task["details"] = details

                            # 移动任务到合适的列表
                            if new_status == "active" and list_name != "current_tasks":
                                task_list.pop(i)
                                if "current_tasks" not in self._agent_state:
                                    self._agent_state["current_tasks"] = []
                                self._agent_state["current_tasks"].append(task)
                            elif new_status == "completed" and list_name != "completed_tasks":
                                task_list.pop(i)
                                if "completed_tasks" not in self._agent_state:
                                    self._agent_state["completed_tasks"] = []
                                self._agent_state["completed_tasks"].append(task)
                            elif new_status == "pending" and list_name != "pending_tasks":
                                task_list.pop(i)
                                if "pending_tasks" not in self._agent_state:
                                    self._agent_state["pending_tasks"] = []
                                self._agent_state["pending_tasks"].append(task)

                            task_found = True
                            break

                    if task_found:
                        break

            if task_found:
                # 更新记忆
                self._memory[f"task_{task_id}"] = {
                    "task_id": task_id,
                    "status": new_status,
                    "updated_by": self.satellite_id,
                    "updated_time": datetime.now().isoformat(),
                    "details": details
                }

                result = {
                    "success": True,
                    "task_id": task_id,
                    "new_status": new_status,
                    "updated_by": self.satellite_id,
                    "updated_time": datetime.now().isoformat()
                }
            else:
                result = {
                    "success": False,
                    "error": f"任务 {task_id} 未找到"
                }

            return json.dumps(result)

        except Exception as e:
            logger.error(f"❌ 更新任务状态失败: {e}")
            error_result = {"success": False, "error": str(e)}
            return json.dumps(error_result)

    async def receive_missile_target(self, target_info: Dict[str, Any]) -> Dict[str, Any]:
        """接收导弹目标信息并进行处理 - V1.0兼容版本"""
        try:
            target_id = target_info.get("target_id", "Unknown")
            logger.info(f"🎯 卫星 {self.satellite_id} 接收到导弹目标 {target_id}")

            # 将目标信息加入未执行任务列表
            task_info = {
                "task_id": f"track_{target_id}_{uuid.uuid4().hex[:8]}",
                "target_id": target_id,
                "target_info": target_info,
                "priority": target_info.get('priority', 'high'),
                "received_time": datetime.now().isoformat(),
                "status": "pending"
            }

            self._agent_state["pending_tasks"].append(task_info)

            # V1.0兼容性
            self.current_tasks[task_info["task_id"]] = task_info

            # 存储到记忆模块
            self._memory[target_id] = {
                "target_info": target_info,
                "received_time": datetime.now().isoformat()
            }

            # 强制使用ADK智能体进行智能处理
            if not self._llm_agent:
                raise RuntimeError(f"ADK智能体未初始化: {self.satellite_id}")

            try:
                logger.info(f"🤖 使用ADK智能体处理目标 {target_id}")

                # 构建详细的处理请求
                processing_prompt = f"""
收到新的导弹目标 {target_id}，请分析并制定跟踪计划。

目标信息:
- 目标ID: {target_id}
- 优先级: {target_info.get('priority', 'high')}
- 接收时间: {datetime.now().isoformat()}

请使用可用的工具函数:
1. 使用 calculate_visibility 计算对目标的可见性窗口
2. 使用 query_memory 查询相关历史信息
3. 使用 update_task_status 更新任务状态为 'active'

请制定详细的跟踪计划并执行相应的工具调用。
"""

                # 使用ADK智能体处理
                response = await self._llm_agent.run_async(processing_prompt)

                processing_result = {
                    "processing": "adk_agent",
                    "response": response,
                    "agent_id": self.satellite_id,
                    "processing_time": datetime.now().isoformat()
                }

                logger.info(f"✅ ADK智能体处理完成: {target_id}")

            except Exception as e:
                logger.error(f"❌ ADK智能体处理失败: {e}")
                raise RuntimeError(f"ADK智能体处理失败: {e}")

            # 返回处理结果字典
            return {
                "success": True,
                "satellite_id": self.satellite_id,
                "target_id": target_id,
                "task_id": task_info["task_id"],
                "processing_result": processing_result
            }

        except Exception as e:
            logger.error(f"❌ 接收导弹目标失败: {e}")
            return {
                "success": False,
                "satellite_id": self.satellite_id,
                "target_id": target_info.get("target_id", "Unknown"),
                "error": str(e)
            }

    def get_agent_status(self) -> Dict[str, Any]:
        """获取智能体状态 - V1.0兼容版本"""
        return {
            "satellite_id": self.satellite_id,
            "agent_state": self._agent_state.copy(),
            "memory_count": len(self._memory),
            "has_adk_agent": self._llm_agent is not None,
            "capabilities": self.config.get("capabilities", []),
            "resources": self.config.get("resources", {}),
            # V1.0兼容性
            "current_tasks_count": len(self.current_tasks),
            "discussion_groups_count": len(self.discussion_groups)
        }
